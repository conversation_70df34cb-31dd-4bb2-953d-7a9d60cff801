import React, { useState, useEffect } from 'react';
import { supabase, isSupabaseConfigured } from '../lib/supabase';
import { BlogPost, Project, ContactMessage, Testimonial } from '../types/database';
import { Eye, MessageSquare, Star, Briefcase, Mail, Users } from 'lucide-react';

interface DashboardStats {
  totalBlogPosts: number;
  totalProjects: number;
  totalTestimonials: number;
  unreadMessages: number;
  totalViews: number;
}

export const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalBlogPosts: 0,
    totalProjects: 0,
    totalTestimonials: 0,
    unreadMessages: 0,
    totalViews: 0,
  });
  const [recentMessages, setRecentMessages] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isSupabaseConfigured()) {
      setLoading(false);
      return;
    }

    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Fetch stats
      const [blogStats, projectStats, testimonialStats, messageStats] = await Promise.all([
        supabase.from('blog_posts').select('views', { count: 'exact' }),
        supabase.from('projects').select('*', { count: 'exact' }),
        supabase.from('testimonials').select('*', { count: 'exact' }),
        supabase.from('contact_messages').select('*', { count: 'exact' }).eq('read', false),
      ]);

      // Calculate total views
      const totalViews = blogStats.data?.reduce((sum, post) => sum + (post.views || 0), 0) || 0;

      setStats({
        totalBlogPosts: blogStats.count || 0,
        totalProjects: projectStats.count || 0,
        totalTestimonials: testimonialStats.count || 0,
        unreadMessages: messageStats.count || 0,
        totalViews,
      });

      // Fetch recent messages
      const { data: messages } = await supabase
        .from('contact_messages')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);

      setRecentMessages(messages || []);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const markMessageAsRead = async (messageId: string) => {
    try {
      await supabase
        .from('contact_messages')
        .update({ read: true })
        .eq('id', messageId);

      // Update local state
      setRecentMessages(prev =>
        prev.map(msg => msg.id === messageId ? { ...msg, read: true } : msg)
      );
      setStats(prev => ({ ...prev, unreadMessages: prev.unreadMessages - 1 }));
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  if (!isSupabaseConfigured()) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-dark-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Admin Dashboard
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Supabase is not configured. Please set up your environment variables.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-dark-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  const statCards = [
    {
      title: 'Blog Posts',
      value: stats.totalBlogPosts,
      icon: <MessageSquare size={24} />,
      color: 'bg-blue-500',
    },
    {
      title: 'Projects',
      value: stats.totalProjects,
      icon: <Briefcase size={24} />,
      color: 'bg-green-500',
    },
    {
      title: 'Testimonials',
      value: stats.totalTestimonials,
      icon: <Star size={24} />,
      color: 'bg-yellow-500',
    },
    {
      title: 'Unread Messages',
      value: stats.unreadMessages,
      icon: <Mail size={24} />,
      color: 'bg-red-500',
    },
    {
      title: 'Total Views',
      value: stats.totalViews,
      icon: <Eye size={24} />,
      color: 'bg-purple-500',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-900 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Admin Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Overview of your portfolio content and engagement
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          {statCards.map((card, index) => (
            <div
              key={card.title}
              className="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-dark-700"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {card.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {card.value}
                  </p>
                </div>
                <div className={`${card.color} p-3 rounded-lg text-white`}>
                  {card.icon}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Recent Messages */}
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-lg border border-gray-200 dark:border-dark-700">
          <div className="p-6 border-b border-gray-200 dark:border-dark-700">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
              <Mail size={20} className="mr-2" />
              Recent Contact Messages
            </h2>
          </div>
          <div className="p-6">
            {recentMessages.length === 0 ? (
              <p className="text-gray-600 dark:text-gray-300 text-center py-8">
                No messages yet
              </p>
            ) : (
              <div className="space-y-4">
                {recentMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`p-4 rounded-lg border ${
                      message.read
                        ? 'bg-gray-50 dark:bg-dark-700 border-gray-200 dark:border-dark-600'
                        : 'bg-primary-50 dark:bg-primary-900/20 border-primary-200 dark:border-primary-800'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {message.name}
                          </h3>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {message.email}
                          </span>
                          {!message.read && (
                            <span className="px-2 py-1 bg-primary-500 text-white text-xs rounded-full">
                              New
                            </span>
                          )}
                        </div>
                        <p className="font-medium text-gray-800 dark:text-gray-200 mb-2">
                          {message.subject}
                        </p>
                        <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-2">
                          {message.message}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                          {new Date(message.created_at).toLocaleDateString()} at{' '}
                          {new Date(message.created_at).toLocaleTimeString()}
                        </p>
                      </div>
                      {!message.read && (
                        <button
                          onClick={() => markMessageAsRead(message.id)}
                          className="ml-4 px-3 py-1 bg-primary-500 text-white text-sm rounded-lg hover:bg-primary-600 transition-colors"
                        >
                          Mark as Read
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-dark-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Quick Actions
            </h3>
            <div className="space-y-3">
              <button className="w-full px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors">
                Add New Blog Post
              </button>
              <button className="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                Add New Project
              </button>
              <button className="w-full px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors">
                Add Testimonial
              </button>
            </div>
          </div>

          <div className="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-dark-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Content Status
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-300">Published Posts</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {stats.totalBlogPosts}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-300">Active Projects</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {stats.totalProjects}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-300">Client Reviews</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {stats.totalTestimonials}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-dark-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-dark-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Engagement
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-300">Total Views</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {stats.totalViews.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-300">Avg. per Post</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {stats.totalBlogPosts > 0 
                    ? Math.round(stats.totalViews / stats.totalBlogPosts)
                    : 0
                  }
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-300">Contact Rate</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  {((stats.unreadMessages / Math.max(stats.totalViews, 1)) * 100).toFixed(2)}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
