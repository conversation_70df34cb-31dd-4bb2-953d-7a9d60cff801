import React, { useState } from "react";
import { MapPin, Phone, Mail, Globe, Send } from "lucide-react";
import { useContact } from "../hooks/useContact";
import { useAnalytics } from "./GoogleAnalytics";

export const Contact: React.FC = () => {
  const { submitContactForm, isSubmitting } = useContact();
  const { trackContactForm } = useAnalytics();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    trackContactForm("form");
    const result = await submitContactForm(formData);

    if (result.success) {
      // Reset form on success
      setFormData({
        name: "",
        email: "",
        subject: "",
        message: "",
      });
    }
  };

  const contactInfos = [
    {
      icon: <MapPin className="text-primary-500" size={24} />,
      title: "Address",
      content: "Addis Ababa, Ethiopia, POB 13908.",
    },
    {
      icon: <Phone className="text-primary-500" size={24} />,
      title: "Contact Number",
      content: "+************-03",
      link: "tel:+************-03",
    },
    {
      icon: <Mail className="text-primary-500" size={24} />,
      title: "Email Address",
      content: "<EMAIL>",
      link: "mailto:<EMAIL>",
    },
    {
      icon: <Globe className="text-primary-500" size={24} />,
      title: "Website",
      content: "kibrumic.com",
      link: "https://kibrumic.com",
    },
  ];

  return (
    <section
      id="contact-section"
      className="py-20 bg-white dark:bg-dark-900 transition-colors duration-500"
    >
      <div className="container mx-auto px-4">
        <div className="text-center mb-16" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-full mb-6">
            <span className="w-2 h-2 bg-primary-500 rounded-full mr-3 animate-pulse"></span>
            <span className="text-primary-600 dark:text-primary-400 text-sm font-semibold">
              CONTACT
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
            Get In <span className="text-gradient">Touch</span>
          </h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-lg">
            Ready to start your next project? Let's discuss how we can work
            together
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {contactInfos.map((info, index) => (
            <div
              key={index}
              className="group bg-white dark:bg-dark-800 p-6 rounded-2xl shadow-lg hover:shadow-2xl border border-gray-200 dark:border-dark-700 text-center transition-all duration-300 hover:-translate-y-3"
              data-aos="fade-up"
              data-aos-delay={index * 100}
            >
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  {info.icon}
                </div>
              </div>
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white group-hover:text-primary-500 transition-colors duration-300">
                {info.title}
              </h3>
              {info.link ? (
                <a
                  href={info.link}
                  onClick={() =>
                    trackContactForm(
                      info.title === "Contact Number" ? "phone" : "email"
                    )
                  }
                  className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors duration-300 font-medium"
                >
                  {info.content}
                </a>
              ) : (
                <p className="text-gray-600 dark:text-gray-300">
                  {info.content}
                </p>
              )}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Form */}
          <div data-aos="fade-right" data-aos-delay="100">
            <div className="bg-white dark:bg-dark-800 p-8 rounded-2xl shadow-lg border border-gray-200 dark:border-dark-700">
              <h3 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
                Send <span className="text-gradient">Message</span>
              </h3>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Your Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Enter your full name"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-dark-600 rounded-xl bg-white dark:bg-dark-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Enter your email address"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-dark-600 rounded-xl bg-white dark:bg-dark-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Subject
                  </label>
                  <input
                    type="text"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    placeholder="What's this about?"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-dark-600 rounded-xl bg-white dark:bg-dark-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Message
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    placeholder="Tell me about your project..."
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-dark-600 rounded-xl bg-white dark:bg-dark-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 resize-none"
                    required
                  ></textarea>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`
                    w-full inline-flex items-center justify-center px-8 py-4 rounded-xl font-medium transition-all duration-300 transform
                    ${
                      isSubmitting
                        ? "bg-gray-400 dark:bg-gray-600 cursor-not-allowed"
                        : "bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 hover:scale-105 shadow-lg hover:shadow-glow"
                    }
                    text-white
                  `}
                >
                  {isSubmitting ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Sending Message...
                    </>
                  ) : (
                    <>
                      Send Message <Send size={18} className="ml-2" />
                    </>
                  )}
                </button>
              </form>
            </div>
          </div>

          {/* Info Panel */}
          <div data-aos="fade-left" data-aos-delay="200">
            <div className="bg-gradient-to-br from-dark-900 to-dark-800 dark:from-dark-950 dark:to-dark-900 text-white p-8 rounded-2xl shadow-lg h-full flex flex-col justify-center relative overflow-hidden">
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-primary-500/10 rounded-full blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-primary-600/10 rounded-full blur-2xl"></div>

              <div className="relative z-10">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold mb-4">
                    Let's <span className="text-gradient">Connect</span>
                  </h2>
                  <p className="text-gray-300 text-lg leading-relaxed">
                    Have a project in mind or just want to say hi? I'm always
                    excited to discuss new opportunities and ideas.
                  </p>
                </div>

                <div className="flex justify-center mb-8">
                  <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center animate-float shadow-glow">
                    <Send
                      size={32}
                      className="text-white transform -rotate-12"
                    />
                  </div>
                </div>

                <div className="space-y-4 mb-8">
                  <div className="flex items-center justify-center space-x-4 text-gray-300">
                    <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
                    <span>Available for freelance work</span>
                  </div>
                  <div className="flex items-center justify-center space-x-4 text-gray-300">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Usually responds within 24 hours</span>
                  </div>
                </div>

                {/* Map */}
                <div className="rounded-xl overflow-hidden shadow-lg border border-gray-700">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d252230.02028974562!2d38.613328040039065!3d8.963479542403238!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x164b85cef5ab402d%3A0x8467b6b037a24d49!2sAddis%20Ababa%2C%20Ethiopia!5e0!3m2!1sen!2sus!4v1600000000000!5m2!1sen!2sus"
                    width="100%"
                    height="200"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Addis Ababa, Ethiopia"
                  ></iframe>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
