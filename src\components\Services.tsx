import React from "react";
import { Palette, Code, BarChart3 } from "lucide-react";

export const Services: React.FC = () => {
  const services = [
    {
      icon: <Palette size={48} />,
      title: "UI/UX Design",
      description:
        "Creating intuitive, user-friendly interfaces that enhance user experience",
      imageSrc: "src/images/ux.png",
    },
    {
      icon: <Code size={48} />,
      title: "Full-Stack Development",
      description:
        "Building robust applications with modern frontend and backend technologies",
      imageSrc: "src/images/full.png",
    },
    {
      icon: <BarChart3 size={48} />,
      title: "Dashboard Development",
      description:
        "Crafting insightful data visualization dashboards for informed decision-making",
      imageSrc: "src/images/data-v.png",
    },
  ];

  return (
    <section
      id="services-section"
      className="py-20 bg-gray-50 dark:bg-dark-800 transition-colors duration-500"
    >
      <div className="container mx-auto px-4">
        <div className="text-center mb-16" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-full mb-6">
            <span className="w-2 h-2 bg-primary-500 rounded-full mr-3 animate-pulse"></span>
            <span className="text-primary-600 dark:text-primary-400 text-sm font-semibold">
              SERVICES
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
            What I <span className="text-gradient">Do</span>
          </h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-lg">
            I provide comprehensive development services to bring your ideas to
            life
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="group bg-white dark:bg-dark-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl border border-gray-200 dark:border-dark-700 transition-all duration-500 hover:-translate-y-3"
              data-aos="fade-up"
              data-aos-delay={100 + index * 100}
            >
              <div className="p-8">
                <div className="mb-6 flex justify-center">
                  <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 flex items-center justify-center text-primary-500 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    {service.icon}
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-4 text-center text-gray-900 dark:text-white group-hover:text-primary-500 transition-colors duration-300">
                  {service.title}
                </h3>
                <div className="h-40 mx-auto overflow-hidden flex items-center justify-center mb-6 rounded-xl bg-gray-50 dark:bg-dark-800">
                  <img
                    src={service.imageSrc}
                    alt={service.title}
                    className="h-full w-auto object-contain transition-transform duration-700 group-hover:scale-110"
                  />
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-center leading-relaxed mb-6">
                  {service.description}
                </p>
                <div className="text-center">
                  <a
                    href="#contact-section"
                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-full hover:from-primary-600 hover:to-primary-700 font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-glow"
                  >
                    Learn More
                    <svg
                      className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
