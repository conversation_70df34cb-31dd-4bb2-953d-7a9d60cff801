import React, { useState, useEffect } from 'react';
import { BarChart3, <PERSON>, X } from 'lucide-react';

interface AnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
  timestamp: string;
}

export const AnalyticsDebug: React.FC = () => {
  const [events, setEvents] = useState<AnalyticsEvent[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [isMinimized, setIsMinimized] = useState(true);

  useEffect(() => {
    // Check if Google Analytics is loaded
    const checkGA = () => {
      if (typeof window !== 'undefined' && window.gtag) {
        setIsVisible(true);
      }
    };

    checkGA();
    
    // Listen for analytics events (we'll intercept console.log)
    const originalLog = console.log;
    console.log = (...args) => {
      if (args[0] === '📊 Analytics Event:' && args[1]) {
        const event = args[1] as AnalyticsEvent;
        setEvents(prev => [event, ...prev.slice(0, 9)]); // Keep last 10 events
      }
      originalLog.apply(console, args);
    };

    return () => {
      console.log = originalLog;
    };
  }, []);

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {isMinimized ? (
        <button
          onClick={() => setIsMinimized(false)}
          className="bg-primary-500 hover:bg-primary-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
          title="Show Analytics Debug"
        >
          <BarChart3 size={20} />
        </button>
      ) : (
        <div className="bg-white dark:bg-dark-800 rounded-lg shadow-2xl border border-gray-200 dark:border-dark-700 w-80 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-dark-700 bg-primary-50 dark:bg-primary-900/20">
            <div className="flex items-center space-x-2">
              <BarChart3 size={16} className="text-primary-500" />
              <span className="text-sm font-semibold text-gray-900 dark:text-white">
                Analytics Debug
              </span>
            </div>
            <button
              onClick={() => setIsMinimized(true)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X size={16} />
            </button>
          </div>

          {/* Status */}
          <div className="p-3 border-b border-gray-200 dark:border-dark-700">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-gray-600 dark:text-gray-300">
                Google Analytics Active (ID: {import.meta.env.VITE_GA_TRACKING_ID})
              </span>
            </div>
          </div>

          {/* Events List */}
          <div className="max-h-64 overflow-y-auto">
            {events.length === 0 ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                <Eye size={24} className="mx-auto mb-2 opacity-50" />
                <p className="text-xs">No events tracked yet</p>
                <p className="text-xs mt-1">Navigate around to see analytics events</p>
              </div>
            ) : (
              <div className="space-y-1 p-2">
                {events.map((event, index) => (
                  <div
                    key={index}
                    className="bg-gray-50 dark:bg-dark-700 rounded p-2 text-xs border border-gray-200 dark:border-dark-600"
                  >
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-semibold text-primary-600 dark:text-primary-400">
                        {event.action}
                      </span>
                      <span className="text-gray-500 dark:text-gray-400">
                        {event.timestamp}
                      </span>
                    </div>
                    <div className="text-gray-600 dark:text-gray-300">
                      <span className="font-medium">Category:</span> {event.category}
                      {event.label && (
                        <>
                          <br />
                          <span className="font-medium">Label:</span> {event.label}
                        </>
                      )}
                      {event.value && (
                        <>
                          <br />
                          <span className="font-medium">Value:</span> {event.value}
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-2 border-t border-gray-200 dark:border-dark-700 bg-gray-50 dark:bg-dark-700">
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
              Events: {events.length} | Real-time tracking active
            </p>
          </div>
        </div>
      )}
    </div>
  );
};
