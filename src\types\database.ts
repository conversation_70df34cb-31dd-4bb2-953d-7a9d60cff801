export interface Database {
  public: {
    Tables: {
      blog_posts: {
        Row: {
          id: string;
          title: string;
          excerpt: string;
          content: string;
          image_url: string;
          slug: string;
          published: boolean;
          created_at: string;
          updated_at: string;
          author_id: string;
          tags: string[];
          read_time: number;
          views: number;
        };
        Insert: {
          id?: string;
          title: string;
          excerpt: string;
          content: string;
          image_url: string;
          slug: string;
          published?: boolean;
          created_at?: string;
          updated_at?: string;
          author_id: string;
          tags?: string[];
          read_time?: number;
          views?: number;
        };
        Update: {
          id?: string;
          title?: string;
          excerpt?: string;
          content?: string;
          image_url?: string;
          slug?: string;
          published?: boolean;
          created_at?: string;
          updated_at?: string;
          author_id?: string;
          tags?: string[];
          read_time?: number;
          views?: number;
        };
      };
      projects: {
        Row: {
          id: string;
          title: string;
          description: string;
          image_url: string;
          category: string;
          technologies: string[];
          github_url?: string;
          live_url?: string;
          featured: boolean;
          created_at: string;
          updated_at: string;
          order_index: number;
        };
        Insert: {
          id?: string;
          title: string;
          description: string;
          image_url: string;
          category: string;
          technologies?: string[];
          github_url?: string;
          live_url?: string;
          featured?: boolean;
          created_at?: string;
          updated_at?: string;
          order_index?: number;
        };
        Update: {
          id?: string;
          title?: string;
          description?: string;
          image_url?: string;
          category?: string;
          technologies?: string[];
          github_url?: string;
          live_url?: string;
          featured?: boolean;
          created_at?: string;
          updated_at?: string;
          order_index?: number;
        };
      };
      contact_messages: {
        Row: {
          id: string;
          name: string;
          email: string;
          subject: string;
          message: string;
          created_at: string;
          read: boolean;
        };
        Insert: {
          id?: string;
          name: string;
          email: string;
          subject: string;
          message: string;
          created_at?: string;
          read?: boolean;
        };
        Update: {
          id?: string;
          name?: string;
          email?: string;
          subject?: string;
          message?: string;
          created_at?: string;
          read?: boolean;
        };
      };
      testimonials: {
        Row: {
          id: string;
          name: string;
          position: string;
          company: string;
          content: string;
          avatar_url?: string;
          rating: number;
          featured: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          position: string;
          company: string;
          content: string;
          avatar_url?: string;
          rating?: number;
          featured?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          position?: string;
          company?: string;
          content?: string;
          avatar_url?: string;
          rating?: number;
          featured?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

// Type helpers
export type BlogPost = Database['public']['Tables']['blog_posts']['Row'];
export type Project = Database['public']['Tables']['projects']['Row'];
export type ContactMessage = Database['public']['Tables']['contact_messages']['Row'];
export type Testimonial = Database['public']['Tables']['testimonials']['Row'];

export type InsertBlogPost = Database['public']['Tables']['blog_posts']['Insert'];
export type InsertProject = Database['public']['Tables']['projects']['Insert'];
export type InsertContactMessage = Database['public']['Tables']['contact_messages']['Insert'];
export type InsertTestimonial = Database['public']['Tables']['testimonials']['Insert'];
