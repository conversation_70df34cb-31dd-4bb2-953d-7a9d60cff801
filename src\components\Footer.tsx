import React from "react";
import {
  Twitter,
  Facebook,
  Instagram,
  Linkedin,
  Github,
  Send,
  MapPin,
  Phone,
  Mail,
  ChevronRight,
} from "lucide-react";

export const Footer: React.FC = () => {
  const socialLinks = [
    { icon: <Twitter size={20} />, href: "#" },
    { icon: <Facebook size={20} />, href: "#" },
    { icon: <Instagram size={20} />, href: "#" },
    { icon: <Linkedin size={20} />, href: "#" },
    { icon: <Github size={20} />, href: "https://github.com/KibruMichael" },
    { icon: <Send size={20} />, href: "https://t.me/+2gQlKrJVVE1kZDFk" },
  ];

  const pageLinks = [
    { name: "Home", href: "#home-section" },
    { name: "About", href: "#about-section" },
    { name: "Services", href: "#services-section" },
    { name: "Projects", href: "#projects-section" },
    { name: "Contact", href: "#contact-section" },
  ];

  const serviceLinks = [
    { name: "UI/UX Design", href: "#" },
    { name: "Full-Stack Development", href: "#" },
    { name: "Dashboard Development", href: "#" },
  ];

  return (
    <footer className="bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 dark:from-dark-950 dark:via-dark-900 dark:to-dark-950 text-gray-300 pt-20 pb-8 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-64 h-64 bg-primary-600/5 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-16">
          {/* About */}
          <div>
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">
                <span className="text-gradient">kbics</span>
              </h2>
              <div className="w-16 h-1 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full"></div>
            </div>
            <p className="mb-6 text-gray-400 leading-relaxed">
              Full-stack developer passionate about creating innovative
              solutions with clean code, modern technologies, and exceptional
              user experiences.
            </p>
            <div className="flex gap-3">
              {socialLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group w-12 h-12 rounded-xl bg-dark-800 dark:bg-dark-700 flex items-center justify-center text-gray-400 hover:bg-gradient-to-r hover:from-primary-500 hover:to-primary-600 hover:text-white transition-all duration-300 transform hover:-translate-y-1 hover:shadow-glow"
                >
                  <div className="group-hover:scale-110 transition-transform duration-300">
                    {link.icon}
                  </div>
                </a>
              ))}
            </div>
          </div>

          {/* Links */}
          <div>
            <h2 className="text-xl font-bold text-white mb-6 relative">
              Links
              <span className="absolute -bottom-2 left-0 w-12 h-1 bg-orange-500"></span>
            </h2>
            <ul className="space-y-3">
              {pageLinks.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="inline-flex items-center text-gray-400 hover:text-orange-500 transition-colors duration-300"
                  >
                    <ChevronRight size={16} className="mr-2" />
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h2 className="text-xl font-bold text-white mb-6 relative">
              Services
              <span className="absolute -bottom-2 left-0 w-12 h-1 bg-orange-500"></span>
            </h2>
            <ul className="space-y-3">
              {serviceLinks.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="inline-flex items-center text-gray-400 hover:text-orange-500 transition-colors duration-300"
                  >
                    <ChevronRight size={16} className="mr-2" />
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Questions */}
          <div>
            <h2 className="text-xl font-bold text-white mb-6 relative">
              Have a Questions?
              <span className="absolute -bottom-2 left-0 w-12 h-1 bg-orange-500"></span>
            </h2>
            <ul className="space-y-4">
              <li>
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex text-gray-400 hover:text-orange-500 transition-colors duration-300"
                >
                  <Mail
                    size={20}
                    className="mr-3 mt-1 flex-shrink-0 text-orange-500"
                  />
                  <span><EMAIL></span>
                </a>
              </li>
              <li>
                <a
                  href="tel:+************-03"
                  className="inline-flex text-gray-400 hover:text-orange-500 transition-colors duration-300"
                >
                  <Phone
                    size={20}
                    className="mr-3 mt-1 flex-shrink-0 text-orange-500"
                  />
                  <span>+251-911571403</span>
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 pt-8 text-center">
          <p className="text-sm text-gray-500">
            © {new Date().getFullYear()} Kibru Michael. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};
