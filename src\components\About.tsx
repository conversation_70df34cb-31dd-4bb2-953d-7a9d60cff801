import React from "react";

export const About: React.FC = () => {
  return (
    <section
      id="about-section"
      className="py-20 bg-white dark:bg-dark-900 transition-colors duration-500"
    >
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row gap-12 items-center">
          {/* Image Column */}
          <div
            className="w-full md:w-5/12"
            data-aos="fade-right"
            data-aos-delay="100"
          >
            <div className="relative overflow-hidden rounded-2xl shadow-2xl h-96 md:h-[500px] group">
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent z-10"></div>
              <img
                src="src/images/kebnew.png"
                alt="Kibru Michael"
                className="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110"
              />
              <div className="absolute top-0 left-0 w-20 h-20 border-t-4 border-l-4 border-primary-500 z-20"></div>
              <div className="absolute bottom-0 right-0 w-1/3 h-1/3 border-b-4 border-r-4 border-orange-500 z-20"></div>
            </div>
          </div>

          {/* Text Column */}
          <div
            className="w-full md:w-7/12"
            data-aos="fade-left"
            data-aos-delay="200"
          >
            <div className="inline-flex items-center px-4 py-2 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-full mb-6">
              <span className="w-2 h-2 bg-primary-500 rounded-full mr-3 animate-pulse"></span>
              <span className="text-primary-600 dark:text-primary-400 text-sm font-semibold">
                ABOUT ME
              </span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white leading-tight">
              Personal <span className="text-gradient">Details</span>
            </h2>

            <p className="text-gray-600 dark:text-gray-300 mb-8 text-lg leading-relaxed">
              Full-stack developer passionate about creating clean, efficient
              code and building modern, user-friendly applications that solve
              real-world problems.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="group p-4 bg-gray-50 dark:bg-dark-800 rounded-xl border border-gray-200 dark:border-dark-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-300">
                <span className="font-semibold text-gray-900 dark:text-white block mb-1">
                  Name
                </span>
                <span className="text-gray-600 dark:text-gray-300">
                  Kibru Michael
                </span>
              </div>

              <div className="group p-4 bg-gray-50 dark:bg-dark-800 rounded-xl border border-gray-200 dark:border-dark-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-300">
                <span className="font-semibold text-gray-900 dark:text-white block mb-1">
                  Location
                </span>
                <span className="text-gray-600 dark:text-gray-300">
                  Addis Ababa, Ethiopia
                </span>
              </div>

              <div className="group p-4 bg-gray-50 dark:bg-dark-800 rounded-xl border border-gray-200 dark:border-dark-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-300">
                <span className="font-semibold text-gray-900 dark:text-white block mb-1">
                  Email
                </span>
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary-500 hover:text-primary-600 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-300"
                >
                  <EMAIL>
                </a>
              </div>

              <div className="group p-4 bg-gray-50 dark:bg-dark-800 rounded-xl border border-gray-200 dark:border-dark-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-300">
                <span className="font-semibold text-gray-900 dark:text-white block mb-1">
                  Phone
                </span>
                <a
                  href="tel:+************-03"
                  className="text-primary-500 hover:text-primary-600 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-300"
                >
                  +************-03
                </a>
              </div>
            </div>

            <div
              className="bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 p-8 rounded-2xl border border-primary-200 dark:border-primary-800 shadow-lg"
              data-aos="zoom-in"
              data-aos-delay="300"
            >
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0">
                  <span className="text-6xl font-bold text-gradient">37+</span>
                </div>
                <div>
                  <p className="text-gray-700 dark:text-gray-300 text-lg font-medium">
                    Projects Completed
                  </p>
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    Successfully delivered to satisfied clients
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
