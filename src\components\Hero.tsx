import React, { useState, useEffect } from "react";
import { ArrowRight } from "lucide-react";

export const Hero: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const slides = [
    {
      title: "I'm <PERSON><PERSON><PERSON> Michael",
      subtitle: "A Freelance Full Stack Developer",
      image: "src/images/wef.gif",
    },
    {
      title: "I'm a Full Stack Developer",
      subtitle: "based in Addis Ababa",
      image: "src/images/log.png",
    },
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [slides.length]);

  return (
    <section
      id="home-section"
      className="min-h-screen flex items-center bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 dark:from-dark-950 dark:via-dark-900 dark:to-dark-950 relative overflow-hidden"
    >
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary-500/10 rounded-full blur-3xl animate-float"></div>
        <div
          className="absolute bottom-20 right-10 w-96 h-96 bg-primary-600/5 rounded-full blur-3xl animate-float"
          style={{ animationDelay: "2s" }}
        ></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-primary-500/5 rounded-full blur-3xl animate-pulse-slow"></div>
      </div>

      <div className="absolute inset-0 bg-gradient-to-br from-black/50 to-transparent"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col md:flex-row items-center gap-12">
          <div
            className="w-full md:w-1/2 text-center md:text-left"
            data-aos="fade-up"
            data-aos-delay="200"
            data-aos-duration="1000"
          >
            <div className="inline-flex items-center px-4 py-2 bg-primary-500/10 border border-primary-500/20 rounded-full mb-6">
              <span className="w-2 h-2 bg-primary-500 rounded-full mr-3 animate-pulse"></span>
              <span className="text-primary-400 text-sm font-medium">
                Available for freelance work
              </span>
            </div>

            <div className="h-32 mb-8">
              {slides.map((slide, index) => (
                <div
                  key={index}
                  className={`transition-all duration-1000 ${
                    currentSlide === index
                      ? "opacity-100 translate-y-0"
                      : "opacity-0 translate-y-8 absolute"
                  }`}
                >
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 leading-tight">
                    <span className="block">
                      {slide.title.split(" ").slice(0, 2).join(" ")}
                    </span>
                    <span className="text-gradient">
                      {slide.title.split(" ").slice(2).join(" ")}
                    </span>
                  </h1>
                  <h2 className="text-xl md:text-2xl text-gray-300 mb-6 leading-relaxed">
                    {slide.subtitle}
                  </h2>
                </div>
              ))}
            </div>

            <div className="flex flex-wrap gap-4 justify-center md:justify-start">
              <a
                href="#contact-section"
                className="group relative bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-8 py-4 rounded-full transition-all duration-300 flex items-center font-medium shadow-lg hover:shadow-glow transform hover:-translate-y-1"
              >
                <span className="relative z-10">Hire me</span>
                <div className="absolute inset-0 bg-gradient-to-r from-primary-600 to-primary-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </a>
              <a
                href="#projects-section"
                className="group border-2 border-white/20 text-white px-8 py-4 rounded-full hover:bg-white hover:text-dark-900 transition-all duration-300 flex items-center font-medium backdrop-blur-sm transform hover:-translate-y-1"
              >
                My works
                <ArrowRight
                  className="ml-2 group-hover:translate-x-1 transition-transform duration-300"
                  size={16}
                />
              </a>
            </div>
          </div>

          <div
            className="w-full md:w-1/2 flex justify-center"
            data-aos="fade-left"
            data-aos-delay="400"
            data-aos-duration="1000"
          >
            <div className="relative w-72 h-72 md:w-96 md:h-96">
              {/* Decorative elements */}
              <div className="absolute -top-4 -left-4 w-24 h-24 border-t-4 border-l-4 border-primary-500 rounded-tl-3xl"></div>
              <div className="absolute -bottom-4 -right-4 w-24 h-24 border-b-4 border-r-4 border-primary-500 rounded-br-3xl"></div>

              {slides.map((slide, index) => (
                <div
                  key={index}
                  className={`
                    absolute inset-0 rounded-3xl overflow-hidden
                    transform transition-all duration-1000
                    ${
                      currentSlide === index
                        ? "opacity-100 scale-100 rotate-0"
                        : "opacity-0 scale-95 rotate-3"
                    }
                  `}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-500/20 to-primary-600/30 animate-pulse"></div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <img
                    src={slide.image}
                    alt="Profile"
                    className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-700"
                  />
                </div>
              ))}

              <div className="absolute inset-0 rounded-3xl border-2 border-primary-500/30 animate-pulse"></div>
              <div className="absolute -inset-2 rounded-3xl border border-white/10 animate-ping-slow"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
