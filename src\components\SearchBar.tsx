import React, { useState } from 'react';
import { Search } from 'lucide-react';
import { useDebounce } from 'use-debounce';

interface SearchBarProps {
  onSearch: (term: string) => void;
}

export const SearchBar: React.FC<SearchBarProps> = ({ onSearch }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedValue] = useDebounce(searchTerm, 300);

  React.useEffect(() => {
    onSearch(debouncedValue);
  }, [debouncedValue, onSearch]);

  return (
    <div className="relative max-w-md mx-auto mb-8">
      <input
        type="search"
        placeholder="Search posts..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="w-full px-4 py-2 pl-10 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
        aria-label="Search blog posts"
      />
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
    </div>
  );
};