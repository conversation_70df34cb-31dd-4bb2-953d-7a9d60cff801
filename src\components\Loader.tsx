import React, { useEffect, useState } from 'react';

export const Loader: React.FC = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate page loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Disable scrolling when loader is active
    if (loading) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [loading]);

  return (
    <div 
      className={`fixed inset-0 z-50 bg-white flex items-center justify-center transition-opacity duration-500 ${
        loading ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
    >
      <div className="w-16 h-16 relative">
        <svg className="circular" viewBox="25 25 50 50">
          <circle
            className="path-bg"
            cx="50"
            cy="50"
            r="20"
            fill="none"
            strokeWidth="3"
            stroke="#eeeeee"
          />
          <circle
            className="path animate-dash"
            cx="50"
            cy="50"
            r="20"
            fill="none"
            strokeWidth="3"
            strokeMiterlimit="10"
            stroke="#F96D00"
          />
        </svg>
      </div>
    </div>
  );
};