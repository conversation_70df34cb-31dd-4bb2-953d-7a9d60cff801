import React from "react";
import { Sun, Moon } from "lucide-react";
import { useTheme } from "../hooks/useTheme";

export const ThemeToggle: React.FC = () => {
  const { theme, setTheme } = useTheme();

  return (
    <button
      aria-label={`Switch to ${theme === "light" ? "dark" : "light"} theme`}
      className="fixed bottom-6 right-6 p-4 rounded-full bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg hover:shadow-glow transition-all duration-300 z-50 transform hover:scale-110 hover:-translate-y-1 group"
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
    >
      <div className="relative">
        {theme === "light" ? (
          <Moon
            size={20}
            className="transition-transform duration-300 group-hover:rotate-12"
          />
        ) : (
          <Sun
            size={20}
            className="transition-transform duration-300 group-hover:rotate-180"
          />
        )}
      </div>
      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary-600 to-primary-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    </button>
  );
};
