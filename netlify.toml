[build]
  # Directory to change to before starting a build
  base = "."
  
  # Directory that contains the deploy-ready HTML files and assets
  publish = "dist"
  
  # Default build command
  command = "npm run build"

[build.environment]
  # Node.js version
  NODE_VERSION = "18"

# Redirect rules for single-page application
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Environment variables (you'll set these in Netlify dashboard)
# VITE_SUPABASE_URL = "your_supabase_url"
# VITE_SUPABASE_ANON_KEY = "your_supabase_anon_key"
# VITE_GA_TRACKING_ID = "your_google_analytics_id"
