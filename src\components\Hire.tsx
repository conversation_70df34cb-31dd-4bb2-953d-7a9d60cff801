import React from 'react';

export const Hire: React.FC = () => {
  return (
    <section className="py-24 bg-gray-900 bg-opacity-90 relative">
      <div className="absolute inset-0 bg-fixed opacity-20" style={{ backgroundImage: 'url(images/bg_1.jpg)' }}></div>
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-2xl mx-auto text-center" data-aos="fade-up">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            I'm <span className="text-orange-500">Available</span> for freelancing
          </h2>
          
          <div className="relative inline-block group">
            <div className="absolute inset-0 bg-orange-500 rounded-md blur-sm group-hover:blur-md transition-all duration-300"></div>
            <a 
              href="#contact-section" 
              className="relative inline-block px-8 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-md transition-all duration-300 group-hover:translate-y-[-2px]"
            >
              Hire me
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};