import { useState, useEffect } from "react";
import { supabase, isSupabaseConfigured } from "../lib/supabase";
import { BlogPost } from "../types/database";

// Mock data for when Supabase is not configured
const mockBlogPosts: BlogPost[] = [
  {
    id: "1",
    title: "The Rise of Serverless Architecture",
    excerpt:
      "Serverless architecture has been gaining significant traction in the web development world, and for good reason.",
    content: "Full content here...",
    image_url: "src/images/image1.jpg",
    slug: "rise-of-serverless-architecture",
    published: true,
    created_at: "2023-06-21T00:00:00Z",
    updated_at: "2023-06-21T00:00:00Z",
    author_id: "admin",
    tags: ["serverless", "architecture", "web-development"],
    read_time: 5,
    views: 150,
  },
  {
    id: "2",
    title: "Demystifying Machine Learning for Web Developers",
    excerpt:
      'The term "Machine Learning" can sound intimidating, but at its core, it\'s about enabling computers to learn from data without explicit programming.',
    content: "Full content here...",
    image_url: "src/images/image2.jpg",
    slug: "demystifying-machine-learning",
    published: true,
    created_at: "2023-07-15T00:00:00Z",
    updated_at: "2023-07-15T00:00:00Z",
    author_id: "admin",
    tags: ["machine-learning", "ai", "web-development"],
    read_time: 8,
    views: 230,
  },
  {
    id: "3",
    title: "The Synergy Between AI and Full-Stack Development",
    excerpt:
      "The intersection of Artificial Intelligence (AI) and full-stack development is creating exciting new possibilities.",
    content: "Full content here...",
    image_url: "src/images/image3.jpg",
    slug: "ai-fullstack-synergy",
    published: true,
    created_at: "2023-08-03T00:00:00Z",
    updated_at: "2023-08-03T00:00:00Z",
    author_id: "admin",
    tags: ["ai", "full-stack", "development"],
    read_time: 6,
    views: 180,
  },
];

export const useBlogPosts = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!isSupabaseConfigured()) {
          // Use mock data when Supabase is not configured
          setPosts(mockBlogPosts);
          setLoading(false);
          return;
        }

        const { data, error } = await supabase
          .from("blog_posts")
          .select("*")
          .eq("published", true)
          .order("created_at", { ascending: false });

        if (error) {
          throw error;
        }

        setPosts(data || []);
      } catch (err) {
        console.error("Error fetching blog posts:", err);
        setError(
          err instanceof Error ? err.message : "Failed to fetch blog posts"
        );
        // Fallback to mock data on error
        setPosts(mockBlogPosts);
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, []);

  const getPostBySlug = (slug: string) => {
    return posts.find((post) => post.slug === slug);
  };

  const getRecentPosts = (limit: number = 3) => {
    return posts.slice(0, limit);
  };

  const getPostsByTag = (tag: string) => {
    return posts.filter((post) => post.tags.includes(tag));
  };

  const incrementViews = async (slug: string) => {
    if (!isSupabaseConfigured()) {
      return; // Skip in mock mode
    }

    try {
      await supabase.rpc("increment_blog_views", { post_slug: slug });
    } catch (error) {
      console.error("Error incrementing blog views:", error);
    }
  };

  return {
    posts,
    loading,
    error,
    getPostBySlug,
    getRecentPosts,
    getPostsByTag,
    incrementViews,
  };
};
