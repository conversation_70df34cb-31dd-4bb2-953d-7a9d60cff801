import React, { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

export const Resume: React.FC = () => {
  const [activeSlide1, setActiveSlide1] = useState(0);
  const [activeSlide2, setActiveSlide2] = useState(0);

  const certifications1 = [
    { id: 1, image: "src/images/powerBI .png", alt: "PowerBI" },
    { id: 2, image: "src/images/mic1.png", alt: "Microsoft Certification 1" },
    { id: 3, image: "src/images/mic2.png", alt: "Microsoft Certification 2" },
    { id: 4, image: "src/images/mic3.png", alt: "Microsoft Certification 3" },
  ];

  const certifications2 = [
    { id: 1, image: "src/images/web3.png", alt: "Web Development" },
    { id: 2, image: "src/images/mic1.png", alt: "Another Certification" },
  ];

  const handlePrevSlide = (slider: number) => {
    if (slider === 1) {
      setActiveSlide1((prev) =>
        prev === 0 ? certifications1.length - 1 : prev - 1
      );
    } else {
      setActiveSlide2((prev) =>
        prev === 0 ? certifications2.length - 1 : prev - 1
      );
    }
  };

  const handleNextSlide = (slider: number) => {
    if (slider === 1) {
      setActiveSlide1((prev) =>
        prev === certifications1.length - 1 ? 0 : prev + 1
      );
    } else {
      setActiveSlide2((prev) =>
        prev === certifications2.length - 1 ? 0 : prev + 1
      );
    }
  };

  return (
    <section id="resume-section" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16" data-aos="fade-up">
          <h6 className="text-orange-500 font-bold mb-2">RESUME</h6>
          <h2 className="text-4xl font-bold mb-4">My Qualifications</h2>
          <div className="w-20 h-1 bg-orange-500 mx-auto"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left Column */}
          <div data-aos="fade-right" data-aos-delay="100">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
              <div className="p-6">
                <span className="inline-block px-3 py-1 bg-orange-100 text-orange-500 rounded-full text-xs font-semibold mb-4">
                  2014-2015
                </span>
                <h3 className="text-xl font-bold mb-2">PowerBI & Microsoft</h3>
                <p className="text-gray-500 mb-4">Cambridge University</p>

                <div className="relative overflow-hidden rounded-lg">
                  {certifications1.map((cert, index) => (
                    <div
                      key={cert.id}
                      className={`transition-opacity duration-500 ${
                        index === activeSlide1 ? "block" : "hidden"
                      }`}
                    >
                      <img
                        src={cert.image}
                        alt={cert.alt}
                        className="w-full h-auto rounded-lg"
                      />
                    </div>
                  ))}

                  <button
                    onClick={() => handlePrevSlide(1)}
                    className="absolute left-2 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-70"
                  >
                    <ChevronLeft size={20} />
                  </button>

                  <button
                    onClick={() => handleNextSlide(1)}
                    className="absolute right-2 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-70"
                  >
                    <ChevronRight size={20} />
                  </button>

                  <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
                    {certifications1.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setActiveSlide1(index)}
                        className={`w-2 h-2 rounded-full ${
                          index === activeSlide1
                            ? "bg-orange-500"
                            : "bg-white bg-opacity-50"
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div data-aos="fade-left" data-aos-delay="200">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="p-6">
                <span className="inline-block px-3 py-1 bg-orange-100 text-orange-500 rounded-full text-xs font-semibold mb-4">
                  2014-2015
                </span>
                <h3 className="text-xl font-bold mb-2">Web Development</h3>
                <p className="text-gray-500 mb-4">Cambridge University</p>

                <div className="relative overflow-hidden rounded-lg">
                  {certifications2.map((cert, index) => (
                    <div
                      key={cert.id}
                      className={`transition-opacity duration-500 ${
                        index === activeSlide2 ? "block" : "hidden"
                      }`}
                    >
                      <img
                        src={cert.image}
                        alt={cert.alt}
                        className="w-full h-auto rounded-lg"
                      />
                    </div>
                  ))}

                  <button
                    onClick={() => handlePrevSlide(2)}
                    className="absolute left-2 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-70"
                  >
                    <ChevronLeft size={20} />
                  </button>

                  <button
                    onClick={() => handleNextSlide(2)}
                    className="absolute right-2 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-70"
                  >
                    <ChevronRight size={20} />
                  </button>

                  <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
                    {certifications2.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setActiveSlide2(index)}
                        className={`w-2 h-2 rounded-full ${
                          index === activeSlide2
                            ? "bg-orange-500"
                            : "bg-white bg-opacity-50"
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
