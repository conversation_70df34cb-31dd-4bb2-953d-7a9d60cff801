# Kibru Michael - World-Class Portfolio & Blog

A modern, responsive portfolio and blog website built with React, TypeScript, and Tailwind CSS, featuring Supabase integration for dynamic content management.

## 🌟 Features

### Core Features
- **Responsive Design**: Fully responsive across all devices
- **Dark/Light Mode**: Seamless theme switching with system preference detection
- **Smooth Animations**: Framer Motion and AOS animations for enhanced UX
- **Modern UI**: Clean, professional design with consistent color scheme
- **Performance Optimized**: Fast loading with optimized assets and code splitting

### Enhanced Sections
- **Hero Section**: Dynamic slideshow with animated backgrounds
- **About**: Personal details with interactive elements
- **Resume**: Professional experience and education
- **Services**: Offered services with engaging visuals
- **Skills**: Animated skill bars with technology icons
- **Projects**: Filterable portfolio with category-based navigation
- **Testimonials**: Client testimonials with auto-rotating carousel
- **Blog**: Dynamic blog with search, filtering, and tagging
- **Contact**: Interactive contact form with real-time validation

### Technical Features
- **Supabase Integration**: Dynamic content management for blogs, projects, and testimonials
- **Search & Filtering**: Advanced search and tag-based filtering for blog posts
- **Toast Notifications**: User-friendly feedback for form submissions
- **Loading States**: Skeleton loading for better perceived performance
- **Error Handling**: Graceful fallbacks and error states
- **SEO Optimized**: Meta tags and semantic HTML structure

## 🚀 Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **Styling**: Tailwind CSS, Custom CSS animations
- **Animations**: Framer Motion, AOS (Animate On Scroll)
- **Backend**: Supabase (PostgreSQL database, Authentication, Real-time)
- **Icons**: Lucide React
- **Date Handling**: date-fns
- **Notifications**: React Hot Toast
- **Utilities**: use-debounce for search optimization

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/kibrumichael/portfolio.git
   cd portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Fill in your Supabase credentials:
   ```env
   VITE_SUPABASE_URL=your_supabase_project_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

## 🗄️ Database Schema

The application uses Supabase with the following tables:

### Blog Posts
```sql
CREATE TABLE blog_posts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  excerpt TEXT NOT NULL,
  content TEXT NOT NULL,
  image_url TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  published BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  author_id TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  read_time INTEGER DEFAULT 5,
  views INTEGER DEFAULT 0
);
```

### Projects
```sql
CREATE TABLE projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  image_url TEXT NOT NULL,
  category TEXT NOT NULL,
  technologies TEXT[] DEFAULT '{}',
  github_url TEXT,
  live_url TEXT,
  featured BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  order_index INTEGER DEFAULT 0
);
```

### Contact Messages
```sql
CREATE TABLE contact_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  subject TEXT NOT NULL,
  message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read BOOLEAN DEFAULT false
);
```

### Testimonials
```sql
CREATE TABLE testimonials (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  position TEXT NOT NULL,
  company TEXT NOT NULL,
  content TEXT NOT NULL,
  avatar_url TEXT,
  rating INTEGER DEFAULT 5,
  featured BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🎨 Design System

### Color Palette
- **Primary**: Orange (#F96D00, #ea580c)
- **Dark**: Slate variants (#0f172a to #f8fafc)
- **Accent**: Complementary colors for highlights

### Typography
- **Font Family**: Poppins (Google Fonts)
- **Headings**: Bold weights with gradient text effects
- **Body**: Regular weight with optimal line height

### Animations
- **Micro-interactions**: Hover effects, button animations
- **Page transitions**: Smooth section reveals
- **Loading states**: Skeleton screens and spinners

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Vercel
```bash
npm install -g vercel
vercel --prod
```

### Deploy to Netlify
```bash
npm run build
# Upload dist folder to Netlify
```

## 🔧 Configuration

### Supabase Setup
1. Create a new Supabase project
2. Run the database schema scripts
3. Configure Row Level Security (RLS) policies
4. Add your credentials to `.env`

### Environment Variables
- `VITE_SUPABASE_URL`: Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `VITE_GA_TRACKING_ID`: Google Analytics tracking ID (optional)

## 📱 Features Overview

### Mock Data Fallback
The application gracefully falls back to mock data when Supabase is not configured, ensuring the portfolio works out of the box.

### Responsive Design
- Mobile-first approach
- Tablet and desktop optimizations
- Touch-friendly interactions

### Performance
- Code splitting and lazy loading
- Optimized images and assets
- Minimal bundle size

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Kibru Michael**
- Portfolio: [kibrumic.com](https://kibrumic.com)
- Email: <EMAIL>
- Phone: +************-03
- Location: Addis Ababa, Ethiopia

---

Built with ❤️ using modern web technologies
