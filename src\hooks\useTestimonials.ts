import { useState, useEffect } from 'react';
import { supabase, isSupabaseConfigured } from '../lib/supabase';
import { Testimonial } from '../types/database';

// Mock data for when Supabase is not configured
const mockTestimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    position: 'Product Manager',
    company: 'TechCorp Inc.',
    content: '<PERSON><PERSON><PERSON> delivered an exceptional e-commerce platform that exceeded our expectations. His attention to detail and technical expertise made the entire process smooth and efficient.',
    avatar_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    featured: true,
    created_at: '2023-08-15T00:00:00Z',
    updated_at: '2023-08-15T00:00:00Z',
  },
  {
    id: '2',
    name: '<PERSON>',
    position: 'CEO',
    company: 'StartupXYZ',
    content: 'Working with <PERSON><PERSON><PERSON> was a game-changer for our startup. He built a scalable web application that helped us secure our Series A funding. Highly recommended!',
    avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    featured: true,
    created_at: '2023-07-20T00:00:00Z',
    updated_at: '2023-07-20T00:00:00Z',
  },
  {
    id: '3',
    name: 'Emily <PERSON>',
    position: 'Marketing Director',
    company: 'Creative Agency',
    content: 'Kibru transformed our outdated website into a modern, responsive platform that significantly improved our conversion rates. His design sense is impeccable.',
    avatar_url: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    featured: true,
    created_at: '2023-06-10T00:00:00Z',
    updated_at: '2023-06-10T00:00:00Z',
  },
  {
    id: '4',
    name: 'David Thompson',
    position: 'CTO',
    company: 'FinTech Solutions',
    content: 'Kibru\'s full-stack development skills are outstanding. He delivered a complex financial dashboard with real-time data visualization that our clients love.',
    avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    featured: false,
    created_at: '2023-05-25T00:00:00Z',
    updated_at: '2023-05-25T00:00:00Z',
  },
  {
    id: '5',
    name: 'Lisa Wang',
    position: 'Founder',
    company: 'E-learning Platform',
    content: 'The learning management system Kibru built for us has been instrumental in scaling our online education business. Professional, reliable, and innovative.',
    avatar_url: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    featured: false,
    created_at: '2023-04-15T00:00:00Z',
    updated_at: '2023-04-15T00:00:00Z',
  },
];

export const useTestimonials = () => {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!isSupabaseConfigured()) {
          // Use mock data when Supabase is not configured
          setTestimonials(mockTestimonials);
          setLoading(false);
          return;
        }

        const { data, error } = await supabase
          .from('testimonials')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          throw error;
        }

        setTestimonials(data || []);
      } catch (err) {
        console.error('Error fetching testimonials:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch testimonials');
        // Fallback to mock data on error
        setTestimonials(mockTestimonials);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  const getFeaturedTestimonials = () => {
    return testimonials.filter(testimonial => testimonial.featured);
  };

  const getTestimonialsByRating = (minRating: number = 4) => {
    return testimonials.filter(testimonial => testimonial.rating >= minRating);
  };

  return {
    testimonials,
    loading,
    error,
    getFeaturedTestimonials,
    getTestimonialsByRating,
  };
};
