# Supabase Setup Guide for <PERSON><PERSON><PERSON>

This guide will help you set up Supabase for your portfolio website to enable dynamic content management.

## Prerequisites

- A Supabase account (sign up at [supabase.com](https://supabase.com))
- Basic understanding of SQL
- Your portfolio project cloned and dependencies installed

## Step 1: Create a New Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: `kibru-michael-portfolio`
   - **Database Password**: Create a strong password (save this!)
   - **Region**: Choose the closest to your users
5. Click "Create new project"
6. Wait for the project to be set up (usually 2-3 minutes)

## Step 2: Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (starts with `https://`)
   - **Anon public key** (starts with `eyJ`)

## Step 3: Set Up Environment Variables

1. In your project root, create a `.env` file (if it doesn't exist)
2. Add your Supabase credentials:

```env
VITE_SUPABASE_URL=your_project_url_here
VITE_SUPABASE_ANON_KEY=your_anon_key_here
VITE_GA_TRACKING_ID=your_google_analytics_id_here
```

**Important**: Never commit your `.env` file to version control!

## Step 4: Set Up the Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `supabase-schema.sql` from your project
3. Paste it into the SQL editor
4. Click **Run** to execute the schema

This will create:
- `blog_posts` table for your blog articles
- `projects` table for your portfolio projects
- `contact_messages` table for contact form submissions
- `testimonials` table for client testimonials
- Proper indexes for performance
- Row Level Security (RLS) policies

## Step 5: Add Sample Data (Optional)

1. In the SQL Editor, copy the contents of `supabase-sample-data.sql`
2. Paste and run it to populate your database with sample content
3. This gives you immediate content to work with

## Step 6: Configure Row Level Security

The schema automatically sets up RLS policies, but here's what they do:

### Blog Posts
- **Public Read**: Anyone can read published blog posts
- **Admin Write**: Only authenticated users can create/update posts

### Projects
- **Public Read**: Anyone can view projects
- **Admin Write**: Only authenticated users can manage projects

### Contact Messages
- **Public Insert**: Anyone can submit contact forms
- **Admin Read**: Only authenticated users can read messages

### Testimonials
- **Public Read**: Anyone can view testimonials
- **Admin Write**: Only authenticated users can manage testimonials

## Step 7: Test the Connection

1. Start your development server: `npm run dev`
2. Open your browser and check the console for any Supabase connection errors
3. Navigate through your portfolio to ensure data loads correctly

## Step 8: Managing Content

### Adding Blog Posts

```sql
INSERT INTO blog_posts (
  title, 
  excerpt, 
  content, 
  image_url, 
  slug, 
  published, 
  tags, 
  read_time
) VALUES (
  'Your Blog Title',
  'A brief excerpt of your post...',
  'Full markdown content here...',
  'https://your-image-url.com/image.jpg',
  'your-blog-slug',
  true,
  ARRAY['tag1', 'tag2', 'tag3'],
  5
);
```

### Adding Projects

```sql
INSERT INTO projects (
  title,
  description,
  image_url,
  category,
  technologies,
  github_url,
  live_url,
  featured,
  order_index
) VALUES (
  'Project Title',
  'Project description...',
  'https://your-image-url.com/project.jpg',
  'web',
  ARRAY['React', 'TypeScript', 'Tailwind CSS'],
  'https://github.com/username/repo',
  'https://your-project.com',
  true,
  1
);
```

### Adding Testimonials

```sql
INSERT INTO testimonials (
  name,
  position,
  company,
  content,
  avatar_url,
  rating,
  featured
) VALUES (
  'Client Name',
  'Position',
  'Company Name',
  'Testimonial content...',
  'https://avatar-url.com/avatar.jpg',
  5,
  true
);
```

## Step 9: Production Deployment

1. Add your environment variables to your hosting platform:
   - **Vercel**: Project Settings → Environment Variables
   - **Netlify**: Site Settings → Environment Variables
   - **Heroku**: Settings → Config Vars

2. Ensure your production domain is added to Supabase:
   - Go to **Authentication** → **URL Configuration**
   - Add your production URL to **Site URL**

## Step 10: Backup and Maintenance

1. **Regular Backups**: Supabase automatically backs up your database
2. **Manual Backup**: Go to **Settings** → **Database** → **Backups**
3. **Monitor Usage**: Check **Settings** → **Usage** for limits

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Check your environment variables
   - Ensure the Supabase URL and key are correct
   - Verify your project is active

2. **RLS Policy Errors**
   - Check if RLS is enabled on tables
   - Verify policy conditions
   - Test with the SQL editor

3. **CORS Errors**
   - Add your domain to allowed origins in Supabase
   - Check authentication settings

### Getting Help

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Discord Community](https://discord.supabase.com)
- [GitHub Issues](https://github.com/supabase/supabase/issues)

## Security Best Practices

1. **Never expose your service key** in client-side code
2. **Use RLS policies** to protect sensitive data
3. **Regularly rotate API keys** if compromised
4. **Monitor database activity** in the Supabase dashboard
5. **Use environment variables** for all sensitive data

## Next Steps

Once your Supabase is set up:

1. Customize the sample data with your actual content
2. Add more blog posts and projects
3. Set up authentication for admin features (optional)
4. Configure email notifications for contact forms
5. Set up analytics and monitoring

Your portfolio is now powered by a robust, scalable backend! 🚀
