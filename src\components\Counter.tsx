import React, { useEffect, useRef } from 'react';
import { Award, <PERSON>old<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Coffee } from 'lucide-react';

interface CounterItemProps {
  icon: React.ReactNode;
  number: number;
  text: string;
  delay: number;
}

const CounterItem: React.FC<CounterItemProps> = ({ icon, number, text, delay }) => {
  const countRef = useRef<HTMLSpanElement>(null);
  
  useEffect(() => {
    const counter = countRef.current;
    if (!counter) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            let count = 0;
            const increment = Math.ceil(number / 60); // Adjust for animation speed
            
            const timer = setInterval(() => {
              count += increment;
              if (count > number) count = number;
              counter.textContent = count.toString();
              
              if (count === number) clearInterval(timer);
            }, 30);
            
            observer.disconnect();
          }
        });
      },
      { threshold: 0.3 }
    );
    
    observer.observe(counter);
    
    return () => observer.disconnect();
  }, [number]);

  return (
    <div className="text-center" data-aos="fade-up" data-aos-delay={delay}>
      <div className="mb-4 inline-flex items-center justify-center w-16 h-16 rounded-full bg-orange-100 text-orange-500">
        {icon}
      </div>
      <div>
        <span 
          ref={countRef} 
          className="block text-4xl font-bold mb-1 text-gray-800"
        >
          0
        </span>
        <span className="text-gray-600">{text}</span>
      </div>
    </div>
  );
};

export const Counter: React.FC = () => {
  return (
    <section className="py-20 bg-gray-900 text-white bg-opacity-90 relative">
      <div className="absolute inset-0 bg-fixed opacity-20" style={{ backgroundImage: 'url(images/bg_1.jpg)' }}></div>
      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          <CounterItem 
            icon={<Award size={32} />} 
            number={10} 
            text="Awards" 
            delay={100}
          />
          <CounterItem 
            icon={<FolderCheck size={32} />} 
            number={37} 
            text="Complete Projects" 
            delay={200}
          />
          <CounterItem 
            icon={<Users size={32} />} 
            number={81} 
            text="Happy Customers" 
            delay={300}
          />
          <CounterItem 
            icon={<Coffee size={32} />} 
            number={30} 
            text="Cups of coffee" 
            delay={400}
          />
        </div>
      </div>
    </section>
  );
};