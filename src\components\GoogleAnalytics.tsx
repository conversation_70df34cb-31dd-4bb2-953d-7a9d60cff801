import { useEffect } from "react";

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

interface GoogleAnalyticsProps {
  trackingId?: string;
}

export const GoogleAnalytics: React.FC<GoogleAnalyticsProps> = ({
  trackingId,
}) => {
  useEffect(() => {
    if (!trackingId) {
      console.warn("Google Analytics tracking ID not provided");
      return;
    }

    // Create script element for Google Analytics
    const script1 = document.createElement("script");
    script1.async = true;
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${trackingId}`;
    document.head.appendChild(script1);

    // Initialize Google Analytics
    const script2 = document.createElement("script");
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${trackingId}', {
        page_title: document.title,
        page_location: window.location.href,
      });
    `;
    document.head.appendChild(script2);

    // Cleanup function
    return () => {
      document.head.removeChild(script1);
      document.head.removeChild(script2);
    };
  }, [trackingId]);

  return null;
};

// Analytics utility functions
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", action, {
      event_category: category,
      event_label: label,
      value: value,
    });

    // Console log for debugging (remove in production)
    console.log("📊 Analytics Event:", {
      action,
      category,
      label,
      value,
      timestamp: new Date().toLocaleTimeString(),
    });
  } else {
    console.log("📊 Analytics not loaded, would track:", {
      action,
      category,
      label,
      value,
    });
  }
};

export const trackPageView = (url: string, title?: string) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("config", import.meta.env.VITE_GA_TRACKING_ID, {
      page_path: url,
      page_title: title,
    });
  }
};

// Custom hooks for analytics
export const useAnalytics = () => {
  const trackContactForm = (method: "email" | "phone" | "form") => {
    trackEvent("contact", "engagement", method);
  };

  const trackProjectView = (projectTitle: string, category: string) => {
    trackEvent("project_view", "portfolio", `${category}: ${projectTitle}`);
  };

  const trackBlogRead = (blogTitle: string, readTime: number) => {
    trackEvent("blog_read", "content", blogTitle, readTime);
  };

  const trackDownload = (fileName: string, fileType: string) => {
    trackEvent("download", "file", `${fileType}: ${fileName}`);
  };

  const trackSocialClick = (platform: string) => {
    trackEvent("social_click", "engagement", platform);
  };

  const trackThemeToggle = (theme: "light" | "dark") => {
    trackEvent("theme_toggle", "ui", theme);
  };

  const trackNavigation = (section: string) => {
    trackEvent("navigation", "ui", section);
  };

  return {
    trackContactForm,
    trackProjectView,
    trackBlogRead,
    trackDownload,
    trackSocialClick,
    trackThemeToggle,
    trackNavigation,
  };
};
