-- Sample data for <PERSON><PERSON><PERSON>
-- Run this after the schema setup

-- Insert sample blog posts
INSERT INTO blog_posts (title, excerpt, content, image_url, slug, published, tags, read_time, views) VALUES
(
  'The Rise of Serverless Architecture',
  'Serverless architecture has been gaining significant traction in the web development world, and for good reason.',
  '# The Rise of Serverless Architecture

Serverless architecture has revolutionized how we think about building and deploying applications. In this comprehensive guide, we''ll explore the benefits, challenges, and best practices of serverless development.

## What is Serverless?

Serverless computing is a cloud computing execution model where the cloud provider dynamically manages the allocation of machine resources. Despite its name, servers are still involved, but developers don''t need to manage them.

## Benefits of Serverless

1. **Cost Efficiency**: Pay only for what you use
2. **Scalability**: Automatic scaling based on demand
3. **Reduced Operational Overhead**: No server management required
4. **Faster Time to Market**: Focus on code, not infrastructure

## Popular Serverless Platforms

- AWS Lambda
- Vercel Functions
- Netlify Functions
- Google Cloud Functions

## Conclusion

Serverless architecture offers compelling advantages for modern web development, making it an essential skill for developers in 2024.',
  'src/images/image1.jpg',
  'rise-of-serverless-architecture',
  true,
  ARRAY['serverless', 'architecture', 'web-development', 'cloud'],
  8,
  245
),
(
  'Demystifying Machine Learning for Web Developers',
  'The term "Machine Learning" can sound intimidating, but at its core, it''s about enabling computers to learn from data without explicit programming.',
  '# Demystifying Machine Learning for Web Developers

Machine Learning (ML) is no longer just for data scientists. Modern web developers can leverage ML to create intelligent, user-centric applications.

## Getting Started with ML in Web Development

### 1. Understanding the Basics
- Supervised vs Unsupervised Learning
- Training Data and Models
- Prediction and Classification

### 2. ML Libraries for JavaScript
- **TensorFlow.js**: Run ML models in the browser
- **Brain.js**: Neural networks in JavaScript
- **ML5.js**: Friendly machine learning for creative coding

### 3. Practical Applications
- Image recognition and classification
- Natural language processing
- Recommendation systems
- Predictive analytics

## Building Your First ML Web App

```javascript
import * as tf from "@tensorflow/tfjs";

// Load a pre-trained model
const model = await tf.loadLayersModel("/path/to/model.json");

// Make predictions
const prediction = model.predict(inputData);
```

## Best Practices

1. Start with pre-trained models
2. Understand your data
3. Consider performance implications
4. Implement proper error handling

Machine learning in web development opens up exciting possibilities for creating more intelligent and responsive applications.',
  'src/images/image2.jpg',
  'demystifying-machine-learning',
  true,
  ARRAY['machine-learning', 'ai', 'web-development', 'javascript'],
  12,
  189
),
(
  'The Synergy Between AI and Full-Stack Development',
  'The intersection of Artificial Intelligence (AI) and full-stack development is creating exciting new possibilities.',
  '# The Synergy Between AI and Full-Stack Development

The integration of AI into full-stack development is transforming how we build applications, from intelligent user interfaces to smart backend systems.

## AI in Frontend Development

### Enhanced User Experience
- Personalized content recommendations
- Intelligent search and filtering
- Automated accessibility improvements
- Real-time language translation

### Tools and Technologies
- **React + TensorFlow.js**: Client-side ML
- **Vue.js + ML5.js**: Creative AI applications
- **Svelte + Brain.js**: Lightweight neural networks

## AI in Backend Development

### Intelligent APIs
- Natural language processing endpoints
- Image and video analysis services
- Predictive analytics APIs
- Automated content moderation

### Database Intelligence
- Query optimization
- Automated indexing
- Anomaly detection
- Data pattern recognition

## Full-Stack AI Architecture

```
Frontend (React/Vue/Angular)
    ↓
API Gateway
    ↓
Microservices (Node.js/Python)
    ↓
AI/ML Services (TensorFlow/PyTorch)
    ↓
Database (PostgreSQL/MongoDB)
```

## Real-World Applications

1. **E-commerce**: Product recommendations, price optimization
2. **Healthcare**: Symptom checkers, appointment scheduling
3. **Finance**: Fraud detection, risk assessment
4. **Education**: Personalized learning paths, automated grading

## Getting Started

1. Learn the fundamentals of ML
2. Experiment with pre-built APIs
3. Build simple AI-powered features
4. Scale gradually based on user feedback

The future of full-stack development is intelligent, and developers who embrace AI will build the next generation of transformative applications.',
  'src/images/image3.jpg',
  'ai-fullstack-synergy',
  true,
  ARRAY['ai', 'full-stack', 'development', 'machine-learning'],
  10,
  156
);

-- Insert sample projects
INSERT INTO projects (title, description, image_url, category, technologies, github_url, live_url, featured, order_index) VALUES
(
  'E-Commerce Dashboard',
  'A comprehensive dashboard for managing e-commerce operations with real-time analytics, inventory management, and sales tracking. Features include interactive charts, data visualization, and automated reporting.',
  'src/images/dp1.png',
  'dashboard',
  ARRAY['React', 'TypeScript', 'Node.js', 'PostgreSQL', 'Chart.js', 'Tailwind CSS'],
  'https://github.com/kibrumichael/ecommerce-dashboard',
  'https://ecommerce-dashboard-demo.vercel.app',
  true,
  1
),
(
  'Modern E-Commerce Website',
  'A fully responsive e-commerce platform with advanced filtering, payment integration, user management, and real-time inventory updates. Built with modern web technologies for optimal performance.',
  'src/images/project-1.jpg',
  'web',
  ARRAY['Next.js', 'Tailwind CSS', 'Stripe', 'MongoDB', 'Redux'],
  'https://github.com/kibrumichael/modern-ecommerce',
  'https://modern-ecommerce-demo.vercel.app',
  true,
  2
),
(
  'Task Management Web App',
  'Collaborative task management application with real-time updates, team collaboration features, project tracking, and advanced reporting capabilities.',
  'src/images/project-6.jpg',
  'web',
  ARRAY['Vue.js', 'Express.js', 'Socket.io', 'MySQL', 'Vuex'],
  'https://github.com/kibrumichael/task-manager',
  'https://task-manager-demo.vercel.app',
  true,
  3
),
(
  'Mobile App UI Design',
  'Clean and intuitive mobile app interface design with focus on user experience, accessibility, and modern design principles. Includes complete design system and prototypes.',
  'src/images/project-2.jpg',
  'design',
  ARRAY['Figma', 'Adobe XD', 'Principle', 'InVision'],
  null,
  'https://dribbble.com/shots/mobile-app-ui',
  false,
  4
),
(
  'Portfolio Website Design',
  'Modern portfolio website with dark mode, smooth animations, responsive design, and optimized performance. Features clean typography and engaging interactions.',
  'src/images/project-3.jpg',
  'design',
  ARRAY['React', 'Framer Motion', 'Tailwind CSS', 'Vite'],
  'https://github.com/kibrumichael/portfolio-design',
  'https://portfolio-design-demo.vercel.app',
  false,
  5
),
(
  'Brand Identity & Illustration',
  'Complete brand identity package including logo design, color palette, typography, and marketing materials. Comprehensive brand guidelines and asset library.',
  'src/images/project-4.jpg',
  'design',
  ARRAY['Adobe Illustrator', 'Photoshop', 'InDesign', 'After Effects'],
  null,
  'https://behance.net/gallery/brand-identity',
  false,
  6
);

-- Insert sample testimonials
INSERT INTO testimonials (name, position, company, content, avatar_url, rating, featured) VALUES
(
  'Sarah Johnson',
  'Product Manager',
  'TechCorp Inc.',
  'Kibru delivered an exceptional e-commerce platform that exceeded our expectations. His attention to detail and technical expertise made the entire process smooth and efficient. The dashboard he built has significantly improved our operational efficiency.',
  'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
  5,
  true
),
(
  'Michael Chen',
  'CEO',
  'StartupXYZ',
  'Working with Kibru was a game-changer for our startup. He built a scalable web application that helped us secure our Series A funding. His full-stack expertise and problem-solving skills are outstanding.',
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  5,
  true
),
(
  'Emily Rodriguez',
  'Marketing Director',
  'Creative Agency',
  'Kibru transformed our outdated website into a modern, responsive platform that significantly improved our conversion rates. His design sense is impeccable and his technical implementation flawless.',
  'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
  5,
  true
),
(
  'David Thompson',
  'CTO',
  'FinTech Solutions',
  'Kibru''s full-stack development skills are outstanding. He delivered a complex financial dashboard with real-time data visualization that our clients love. Professional, reliable, and innovative.',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  5,
  false
),
(
  'Lisa Wang',
  'Founder',
  'E-learning Platform',
  'The learning management system Kibru built for us has been instrumental in scaling our online education business. His attention to user experience and technical excellence is remarkable.',
  'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
  5,
  false
);
