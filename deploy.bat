@echo off
echo 🚀 Preparing <PERSON><PERSON><PERSON> for Deployment...
echo ==================================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo ✅ Node.js version:
node --version
echo ✅ npm version:
npm --version
echo.

REM Install dependencies
echo 📦 Installing dependencies...
npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully
echo.

REM Run build
echo 🔨 Building the project...
npm run build

if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo ✅ Build completed successfully
echo.

REM Check if dist folder exists
if not exist "dist" (
    echo ❌ dist folder not found
    pause
    exit /b 1
)

echo 📊 Build Statistics:
echo ===================
dir dist
echo.

echo 🎉 Your portfolio is ready for deployment!
echo ==========================================
echo.
echo 📋 Next Steps:
echo 1. Go to https://netlify.com
echo 2. Sign up/login with GitHub
echo 3. Drag the 'dist' folder to Netlify
echo 4. Configure environment variables
echo 5. Your site will be live!
echo.
echo 🌐 Alternative: Use Git integration
echo 1. Push your code to GitHub
echo 2. Connect repository to Netlify
echo 3. Automatic deployments on every push
echo.
echo 📚 For detailed instructions, see: NETLIFY_DEPLOYMENT.md
echo.
pause
