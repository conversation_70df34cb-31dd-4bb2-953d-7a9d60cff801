# Deployment Guide for <PERSON><PERSON><PERSON> <PERSON>

This guide covers deploying your enhanced portfolio to various hosting platforms with Supabase integration.

## Prerequisites

- ✅ Supabase project set up and configured
- ✅ Environment variables configured locally
- ✅ Google Analytics tracking ID (optional)
- ✅ All features tested locally

## Platform-Specific Deployment

### 1. Vercel (Recommended)

Vercel offers excellent performance and easy deployment for React applications.

#### Steps:

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy**
   ```bash
   vercel --prod
   ```

4. **Set Environment Variables**
   - Go to your Vercel dashboard
   - Select your project
   - Go to Settings → Environment Variables
   - Add:
     ```
     VITE_SUPABASE_URL=your_supabase_url
     VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
     VITE_GA_TRACKING_ID=your_google_analytics_id
     ```

5. **Redeploy**
   ```bash
   vercel --prod
   ```

#### Vercel Configuration (vercel.json)
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

### 2. Netlify

Great for static sites with excellent CI/CD integration.

#### Steps:

1. **Build the project**
   ```bash
   npm run build
   ```

2. **Deploy via Netlify CLI**
   ```bash
   npm install -g netlify-cli
   netlify login
   netlify deploy --prod --dir=dist
   ```

3. **Set Environment Variables**
   - Go to Site Settings → Environment Variables
   - Add the same variables as above

#### Netlify Configuration (_redirects)
```
/*    /index.html   200
```

### 3. GitHub Pages

Free hosting for public repositories.

#### Steps:

1. **Install gh-pages**
   ```bash
   npm install --save-dev gh-pages
   ```

2. **Update package.json**
   ```json
   {
     "homepage": "https://yourusername.github.io/repository-name",
     "scripts": {
       "predeploy": "npm run build",
       "deploy": "gh-pages -d dist"
     }
   }
   ```

3. **Deploy**
   ```bash
   npm run deploy
   ```

**Note**: GitHub Pages doesn't support environment variables for security. Consider using a different platform for the full Supabase functionality.

### 4. Firebase Hosting

Google's hosting platform with excellent performance.

#### Steps:

1. **Install Firebase CLI**
   ```bash
   npm install -g firebase-tools
   ```

2. **Login and Initialize**
   ```bash
   firebase login
   firebase init hosting
   ```

3. **Configure firebase.json**
   ```json
   {
     "hosting": {
       "public": "dist",
       "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
       "rewrites": [
         {
           "source": "**",
           "destination": "/index.html"
         }
       ]
     }
   }
   ```

4. **Build and Deploy**
   ```bash
   npm run build
   firebase deploy
   ```

## Environment Variables Setup

### Required Variables

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Google Analytics (Optional)
VITE_GA_TRACKING_ID=G-XXXXXXXXXX
```

### Platform-Specific Setup

#### Vercel
- Dashboard → Project → Settings → Environment Variables

#### Netlify
- Site Settings → Environment Variables

#### Railway
- Project → Variables

#### Heroku
- Settings → Config Vars

## Domain Configuration

### Custom Domain Setup

1. **Purchase a domain** (recommended: Namecheap, GoDaddy, Google Domains)

2. **Configure DNS**
   - For Vercel: Add CNAME record pointing to `cname.vercel-dns.com`
   - For Netlify: Add CNAME record pointing to your Netlify subdomain

3. **Update Supabase Settings**
   - Go to Authentication → URL Configuration
   - Add your custom domain to Site URL

### SSL Certificate

Most platforms automatically provide SSL certificates. Ensure HTTPS is enabled.

## Performance Optimization

### Build Optimization

1. **Analyze Bundle Size**
   ```bash
   npm run build
   npx vite-bundle-analyzer dist
   ```

2. **Optimize Images**
   - Use WebP format when possible
   - Implement lazy loading
   - Compress images before upload

3. **Code Splitting**
   - Already implemented with React.lazy()
   - Consider route-based splitting for larger apps

### CDN Configuration

Most hosting platforms provide CDN automatically. For additional optimization:

- Use Cloudflare for additional caching
- Optimize cache headers
- Enable compression (gzip/brotli)

## Monitoring and Analytics

### Google Analytics Setup

1. **Create GA4 Property**
   - Go to Google Analytics
   - Create new property
   - Get tracking ID

2. **Add to Environment Variables**
   ```env
   VITE_GA_TRACKING_ID=G-XXXXXXXXXX
   ```

### Error Monitoring

Consider adding error monitoring:

```bash
npm install @sentry/react @sentry/tracing
```

### Performance Monitoring

- Use Lighthouse for performance audits
- Monitor Core Web Vitals
- Set up uptime monitoring

## Security Considerations

### Environment Variables

- ✅ Never commit `.env` files
- ✅ Use different keys for development/production
- ✅ Regularly rotate API keys
- ✅ Use Supabase RLS policies

### Content Security Policy

Add CSP headers for additional security:

```html
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://www.google-analytics.com;">
```

## Troubleshooting

### Common Issues

1. **Environment Variables Not Working**
   - Ensure variables start with `VITE_`
   - Restart development server after changes
   - Check platform-specific variable setup

2. **Supabase Connection Issues**
   - Verify URL and key are correct
   - Check RLS policies
   - Ensure domain is whitelisted

3. **Build Failures**
   - Check for TypeScript errors
   - Verify all dependencies are installed
   - Clear node_modules and reinstall

4. **Routing Issues**
   - Ensure proper redirects are configured
   - Check for conflicting routes
   - Verify base URL configuration

### Debug Commands

```bash
# Check build locally
npm run build && npm run preview

# Analyze bundle
npm run build -- --analyze

# Check for unused dependencies
npx depcheck

# Audit for vulnerabilities
npm audit
```

## Maintenance

### Regular Updates

1. **Dependencies**
   ```bash
   npm update
   npm audit fix
   ```

2. **Supabase**
   - Monitor usage in dashboard
   - Update RLS policies as needed
   - Backup database regularly

3. **Content**
   - Regular blog posts
   - Update project portfolio
   - Refresh testimonials

### Backup Strategy

1. **Code**: Git repository (GitHub/GitLab)
2. **Database**: Supabase automatic backups
3. **Assets**: Cloud storage backup
4. **Environment**: Document all configurations

## Support

If you encounter issues:

1. Check the troubleshooting section
2. Review platform-specific documentation
3. Check Supabase status page
4. Contact support for your hosting platform

---

Your world-class portfolio is now ready for production! 🚀
