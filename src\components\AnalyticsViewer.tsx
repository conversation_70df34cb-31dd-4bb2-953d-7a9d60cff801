import React, { useState, useEffect } from 'react';
import { Bar<PERSON><PERSON>3, <PERSON>, Users, MousePointer, Globe, X, Minimize2, Maximize2 } from 'lucide-react';

interface AnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
  timestamp: string;
}

export const AnalyticsViewer: React.FC = () => {
  const [events, setEvents] = useState<AnalyticsEvent[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [isMinimized, setIsMinimized] = useState(true);
  const [sessionEvents, setSessionEvents] = useState(0);
  const [pageViews, setPageViews] = useState(1);

  useEffect(() => {
    // Check if Google Analytics is loaded
    const checkGA = () => {
      if (typeof window !== 'undefined' && window.gtag) {
        setIsVisible(true);
      }
    };

    checkGA();
    
    // Listen for analytics events
    const originalLog = console.log;
    console.log = (...args) => {
      if (args[0] === '📊 Analytics Event:' && args[1]) {
        const event = args[1] as AnalyticsEvent;
        setEvents(prev => [event, ...prev.slice(0, 19)]); // Keep last 20 events
        setSessionEvents(prev => prev + 1);
      }
      originalLog.apply(console, args);
    };

    // Track page view
    setPageViews(1);

    return () => {
      console.log = originalLog;
    };
  }, []);

  if (!isVisible) {
    return null;
  }

  const getEventIcon = (category: string) => {
    switch (category) {
      case 'ui': return <MousePointer size={12} />;
      case 'engagement': return <Users size={12} />;
      case 'content': return <Eye size={12} />;
      default: return <BarChart3 size={12} />;
    }
  };

  const getEventColor = (category: string) => {
    switch (category) {
      case 'ui': return 'text-blue-500';
      case 'engagement': return 'text-green-500';
      case 'content': return 'text-purple-500';
      case 'portfolio': return 'text-orange-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <div className="fixed bottom-4 left-4 z-50">
      {isMinimized ? (
        <button
          onClick={() => setIsMinimized(false)}
          className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110 group"
          title="Show Analytics Real-Time"
        >
          <div className="relative">
            <BarChart3 size={20} />
            {sessionEvents > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">
                {sessionEvents > 99 ? '99+' : sessionEvents}
              </span>
            )}
          </div>
        </button>
      ) : (
        <div className="bg-white dark:bg-dark-800 rounded-xl shadow-2xl border border-gray-200 dark:border-dark-700 w-96 max-h-[500px] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-dark-700 bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20">
            <div className="flex items-center space-x-2">
              <div className="relative">
                <BarChart3 size={18} className="text-primary-600 dark:text-primary-400" />
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              </div>
              <span className="text-sm font-semibold text-gray-900 dark:text-white">
                Analytics Real-Time
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsMinimized(true)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded"
                title="Minimize"
              >
                <Minimize2 size={14} />
              </button>
              <button
                onClick={() => setIsVisible(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded"
                title="Close"
              >
                <X size={14} />
              </button>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="p-4 border-b border-gray-200 dark:border-dark-700 bg-gray-50 dark:bg-dark-700">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-primary-600 dark:text-primary-400">
                  {pageViews}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-300">Page Views</div>
              </div>
              <div>
                <div className="text-lg font-bold text-green-600 dark:text-green-400">
                  {sessionEvents}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-300">Events</div>
              </div>
              <div>
                <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                  1
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-300">Active User</div>
              </div>
            </div>
          </div>

          {/* GA Status */}
          <div className="p-3 border-b border-gray-200 dark:border-dark-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-gray-600 dark:text-gray-300">
                  Google Analytics Active
                </span>
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                {import.meta.env.VITE_GA_TRACKING_ID}
              </span>
            </div>
          </div>

          {/* Events List */}
          <div className="max-h-64 overflow-y-auto">
            {events.length === 0 ? (
              <div className="p-6 text-center text-gray-500 dark:text-gray-400">
                <Globe size={32} className="mx-auto mb-3 opacity-50" />
                <p className="text-sm font-medium">Waiting for interactions...</p>
                <p className="text-xs mt-1">Navigate around to see real-time events</p>
              </div>
            ) : (
              <div className="space-y-1 p-3">
                <div className="text-xs font-semibold text-gray-600 dark:text-gray-300 mb-2 flex items-center">
                  <Eye size={12} className="mr-1" />
                  Recent Events ({events.length})
                </div>
                {events.map((event, index) => (
                  <div
                    key={index}
                    className="bg-gray-50 dark:bg-dark-700 rounded-lg p-2 text-xs border border-gray-200 dark:border-dark-600 hover:bg-gray-100 dark:hover:bg-dark-600 transition-colors"
                  >
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center space-x-1">
                        <span className={getEventColor(event.category)}>
                          {getEventIcon(event.category)}
                        </span>
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {event.action}
                        </span>
                      </div>
                      <span className="text-gray-500 dark:text-gray-400">
                        {event.timestamp}
                      </span>
                    </div>
                    <div className="text-gray-600 dark:text-gray-300 ml-4">
                      <span className="font-medium">Category:</span> {event.category}
                      {event.label && (
                        <>
                          <br />
                          <span className="font-medium">Label:</span> {event.label}
                        </>
                      )}
                      {event.value && (
                        <>
                          <br />
                          <span className="font-medium">Value:</span> {event.value}
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-3 border-t border-gray-200 dark:border-dark-700 bg-gray-50 dark:bg-dark-700">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500 dark:text-gray-400">
                Real-time tracking active
              </span>
              <a
                href="https://analytics.google.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary-500 hover:text-primary-600 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
              >
                View in GA4 →
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
