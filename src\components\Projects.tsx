import React, { useState } from "react";
import { Link } from "lucide-react";

type Project = {
  id: number;
  title: string;
  category: string;
  image: string;
  size?: "large" | "medium" | "small";
};

export const Projects: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState("all");

  const categories = [
    { id: "all", name: "All Projects" },
    { id: "web", name: "Web Development" },
    { id: "dashboard", name: "Dashboard" },
    { id: "design", name: "UI/UX Design" },
  ];

  const projects: Project[] = [
    {
      id: 1,
      title: "Dashboard Development",
      category: "dashboard",
      image: "src/images/dp1.png",
      size: "large",
    },
    {
      id: 2,
      title: "E-Commerce Website",
      category: "web",
      image: "src/images/project-1.jpg",
      size: "medium",
    },
    {
      id: 3,
      title: "Mobile App UI Design",
      category: "design",
      image: "src/images/project-2.jpg",
      size: "small",
    },
    {
      id: 4,
      title: "Portfolio Design",
      category: "design",
      image: "src/images/project-3.jpg",
      size: "small",
    },
    {
      id: 5,
      title: "Branding & Illustration",
      category: "design",
      image: "src/images/project-4.jpg",
      size: "medium",
    },
    {
      id: 6,
      title: "Web Application",
      category: "web",
      image: "src/images/project-6.jpg",
      size: "medium",
    },
  ];

  const filteredProjects =
    activeCategory === "all"
      ? projects
      : projects.filter((project) => project.category === activeCategory);

  return (
    <section id="projects-section" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16" data-aos="fade-up">
          <h6 className="text-orange-500 font-bold mb-2">PORTFOLIO</h6>
          <h2 className="text-4xl font-bold mb-4">Featured Projects</h2>
          <div className="w-20 h-1 bg-orange-500 mx-auto mb-8"></div>

          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-6 py-2 rounded-full transition-colors duration-300 ${
                  activeCategory === category.id
                    ? "bg-orange-500 text-white"
                    : "bg-gray-100 hover:bg-gray-200 text-gray-700"
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className={`
                relative group overflow-hidden rounded-lg shadow-lg
                ${
                  project.size === "large"
                    ? "col-span-full row-span-2 md:col-span-2"
                    : project.size === "medium"
                    ? "col-span-full md:col-span-2 lg:col-span-2"
                    : ""
                }
              `}
              style={{ height: project.size === "large" ? "500px" : "300px" }}
              data-aos="fade-up"
              data-aos-delay={100 * (project.id % 3)}
            >
              <div
                className="absolute inset-0 bg-cover bg-center transition-transform duration-700 group-hover:scale-110"
                style={{ backgroundImage: `url(${project.image})` }}
              ></div>

              <div className="absolute inset-0 bg-gradient-to-t from-black via-black/50 to-transparent opacity-70 transition-opacity duration-300 group-hover:opacity-90"></div>

              <div className="absolute inset-0 flex flex-col justify-end p-6 transform translate-y-4 transition-transform duration-300 group-hover:translate-y-0">
                <h3 className="text-white text-xl font-bold mb-2">
                  {project.title}
                </h3>
                <p className="text-orange-300 mb-4">
                  {categories.find((c) => c.id === project.category)?.name}
                </p>

                <div className="transform translate-y-8 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100">
                  <a
                    href="#"
                    className="inline-flex items-center gap-2 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-full transition-colors duration-300"
                  >
                    View Project <Link size={16} />
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
