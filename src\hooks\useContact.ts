import { useState } from 'react';
import { supabase, isSupabaseConfigured } from '../lib/supabase';
import { InsertContactMessage } from '../types/database';
import toast from 'react-hot-toast';

export const useContact = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const submitContactForm = async (formData: Omit<InsertContactMessage, 'id' | 'created_at' | 'read'>) => {
    setIsSubmitting(true);

    try {
      if (!isSupabaseConfigured()) {
        // Simulate form submission when Supabase is not configured
        await new Promise(resolve => setTimeout(resolve, 1500));
        toast.success('Message sent successfully! (Demo mode)');
        return { success: true };
      }

      const { error } = await supabase
        .from('contact_messages')
        .insert([formData]);

      if (error) {
        throw error;
      }

      toast.success('Message sent successfully! I\'ll get back to you soon.');
      return { success: true };
    } catch (error) {
      console.error('Error submitting contact form:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    submitContactForm,
    isSubmitting,
  };
};
