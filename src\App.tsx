import React, { Suspense } from "react";
import { Hero } from "./components/Hero";
import { About } from "./components/About";
import { Resume } from "./components/Resume";
import { Services } from "./components/Services";
import { Skills } from "./components/Skills";
import { Projects } from "./components/Projects";
import { Blog } from "./components/Blog";
import { Contact } from "./components/Contact";
import { Footer } from "./components/Footer";
import { Navigation } from "./components/Navigation";
import { Counter } from "./components/Counter";
import { Hire } from "./components/Hire";
import { Loader } from "./components/Loader";
import { ThemeToggle } from "./components/ThemeToggle";
import { AnimatePresence, motion } from "framer-motion";

function App() {
  return (
    <div className="min-h-screen bg-white dark:bg-dark-900 transition-colors duration-500 bg-pattern">
      <Suspense fallback={<Loader />}>
        <AnimatePresence mode="wait">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <Navigation />
            <Hero />
            <About />
            <Resume />
            <Services />
            <Skills />
            <Projects />
            <Blog />
            <Counter />
            <Hire />
            <Contact />
            <Footer />
            <ThemeToggle />
          </motion.div>
        </AnimatePresence>
      </Suspense>
    </div>
  );
}

export default App;
