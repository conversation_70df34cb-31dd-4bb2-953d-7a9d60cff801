import React, { Suspense } from "react";
import { Hero } from "./components/Hero";
import { About } from "./components/About";
import { Resume } from "./components/Resume";
import { Services } from "./components/Services";
import { Skills } from "./components/Skills";
import { Projects } from "./components/Projects";
import { Blog } from "./components/Blog";
import { Contact } from "./components/Contact";
import { Footer } from "./components/Footer";
import { Navigation } from "./components/Navigation";
import { Counter } from "./components/Counter";
import { Hire } from "./components/Hire";
import { Loader } from "./components/Loader";
import { ThemeToggle } from "./components/ThemeToggle";
import { Testimonials } from "./components/Testimonials";
import { GoogleAnalytics } from "./components/GoogleAnalytics";
import { AnimatePresence, motion } from "framer-motion";
import { Toaster } from "react-hot-toast";

function App() {
  return (
    <div className="min-h-screen bg-white dark:bg-dark-900 transition-colors duration-500 bg-pattern">
      <GoogleAnalytics trackingId={import.meta.env.VITE_GA_TRACKING_ID} />
      <Suspense fallback={<Loader />}>
        <AnimatePresence mode="wait">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <Navigation />
            <Hero />
            <About />
            <Resume />
            <Services />
            <Skills />
            <Projects />
            <Testimonials />
            <Blog />
            <Counter />
            <Hire />
            <Contact />
            <Footer />
            <ThemeToggle />
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: "var(--toast-bg)",
                  color: "var(--toast-color)",
                  border: "1px solid var(--toast-border)",
                },
                success: {
                  iconTheme: {
                    primary: "#F96D00",
                    secondary: "#fff",
                  },
                },
              }}
            />
          </motion.div>
        </AnimatePresence>
      </Suspense>
    </div>
  );
}

export default App;
