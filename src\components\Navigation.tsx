import React, { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import { useAnalytics } from "./GoogleAnalytics";

export const Navigation: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState("home-section");
  const { trackNavigation } = useAnalytics();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);

      // Update active section based on scroll position
      const sections = [
        "home-section",
        "about-section",
        "resume-section",
        "services-section",
        "skills-section",
        "projects-section",
        "testimonials-section",
        "blog-section",
        "contact-section",
      ];

      for (const sectionId of sections) {
        const element = document.getElementById(sectionId);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            setActiveSection(sectionId);
            break;
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const navItems = [
    { name: "Home", href: "#home-section", id: "home-section" },
    { name: "About", href: "#about-section", id: "about-section" },
    { name: "Resume", href: "#resume-section", id: "resume-section" },
    { name: "Services", href: "#services-section", id: "services-section" },
    { name: "Skills", href: "#skills-section", id: "skills-section" },
    { name: "Projects", href: "#projects-section", id: "projects-section" },
    {
      name: "Testimonials",
      href: "#testimonials-section",
      id: "testimonials-section",
    },
    { name: "Blog", href: "#blog-section", id: "blog-section" },
    { name: "Contact", href: "#contact-section", id: "contact-section" },
  ];

  return (
    <nav
      className={`fixed w-full z-50 transition-all duration-500 ${
        isScrolled
          ? "bg-white/95 dark:bg-dark-900/95 backdrop-blur-md py-2 shadow-lg border-b border-gray-200/20 dark:border-gray-700/20"
          : "bg-transparent py-4"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center">
          <a
            href="#home-section"
            className={`text-2xl font-bold transition-colors duration-300 ${
              isScrolled ? "text-gray-900 dark:text-white" : "text-white"
            }`}
          >
            <span className="text-gradient">kbics</span>
          </a>

          <button
            className={`md:hidden focus:outline-none transition-colors duration-300 ${
              isScrolled ? "text-gray-900 dark:text-white" : "text-white"
            }`}
            onClick={toggleMobileMenu}
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>

          <div
            className={`
            md:flex items-center space-x-8
            ${
              isMobileMenuOpen
                ? "absolute top-full left-0 right-0 bg-white/95 dark:bg-dark-900/95 backdrop-blur-md py-6 px-4 shadow-lg border-b border-gray-200/20 dark:border-gray-700/20 flex flex-col space-y-4"
                : "hidden"
            }
            md:relative md:bg-transparent md:shadow-none md:space-y-0 md:p-0 md:border-none
          `}
          >
            {navItems.map(({ name, href, id }) => (
              <a
                key={name}
                href={href}
                onClick={() => {
                  setIsMobileMenuOpen(false);
                  trackNavigation(name);
                }}
                className={`
                  relative transition-all duration-300 md:px-0 px-4 py-2 block md:inline font-medium
                  ${
                    isScrolled
                      ? "text-gray-700 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400"
                      : "text-white hover:text-primary-400"
                  }
                  ${
                    activeSection === id
                      ? isScrolled
                        ? "text-primary-500 dark:text-primary-400"
                        : "text-primary-400"
                      : ""
                  }
                `}
              >
                {name}
                {activeSection === id && (
                  <span className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-primary-500 rounded-full md:block hidden"></span>
                )}
              </a>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
};
