import React, { useEffect, useRef } from "react";

interface SkillProps {
  name: string;
  percentage: number;
  color: string;
  delay: number;
}

const Skill: React.FC<SkillProps> = ({ name, percentage, color, delay }) => {
  const progressBarRef = useRef<HTMLDivElement>(null);
  const percentageRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    const progressBar = progressBarRef.current;
    const percentageSpan = percentageRef.current;

    if (!progressBar || !percentageSpan) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              progressBar.style.width = `${percentage}%`;

              let currentValue = 0;
              const increment = percentage / 50; // Adjust for animation speed

              const animateValue = () => {
                currentValue += increment;
                if (currentValue > percentage) {
                  currentValue = percentage;
                }
                percentageSpan.textContent = `${Math.floor(currentValue)}%`;

                if (currentValue < percentage) {
                  requestAnimationFrame(animateValue);
                }
              };

              requestAnimationFrame(animateValue);
            }, delay);

            observer.disconnect();
          }
        });
      },
      { threshold: 0.2 }
    );

    observer.observe(progressBar);

    return () => observer.disconnect();
  }, [percentage, delay]);

  return (
    <div className="mb-8 group" data-aos="fade-up" data-aos-delay={delay}>
      <div className="flex justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-primary-500 transition-colors duration-300">
          {name}
        </h3>
        <span
          ref={percentageRef}
          className="font-bold text-primary-500 bg-primary-50 dark:bg-primary-900/20 px-3 py-1 rounded-full text-sm"
        >
          0%
        </span>
      </div>
      <div className="w-full h-4 bg-gray-200 dark:bg-dark-700 rounded-full overflow-hidden shadow-inner">
        <div
          ref={progressBarRef}
          className={`h-full ${color} rounded-full transition-all duration-1000 ease-out relative overflow-hidden`}
          style={{ width: "0%" }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
        </div>
      </div>
    </div>
  );
};

export const Skills: React.FC = () => {
  const skills = [
    {
      name: "JavaScript",
      percentage: 90,
      color: "bg-gradient-to-r from-yellow-400 to-yellow-600",
    },
    {
      name: "React & Next.js",
      percentage: 85,
      color: "bg-gradient-to-r from-blue-400 to-blue-600",
    },
    {
      name: "HTML5",
      percentage: 95,
      color: "bg-gradient-to-r from-orange-400 to-orange-600",
    },
    {
      name: "CSS3 & Tailwind",
      percentage: 90,
      color: "bg-gradient-to-r from-blue-300 to-blue-500",
    },
    {
      name: "MERN Stack",
      percentage: 90,
      color: "bg-gradient-to-r from-green-400 to-green-600",
    },
    {
      name: "Python",
      percentage: 80,
      color: "bg-gradient-to-r from-blue-500 to-blue-700",
    },
    {
      name: "PowerBI",
      percentage: 80,
      color: "bg-gradient-to-r from-yellow-500 to-yellow-700",
    },
  ];

  return (
    <section
      id="skills-section"
      className="py-20 bg-white dark:bg-dark-900 transition-colors duration-500"
    >
      <div className="container mx-auto px-4">
        <div className="text-center mb-16" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-full mb-6">
            <span className="w-2 h-2 bg-primary-500 rounded-full mr-3 animate-pulse"></span>
            <span className="text-primary-600 dark:text-primary-400 text-sm font-semibold">
              SKILLS
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
            My <span className="text-gradient">Skills</span>
          </h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-lg">
            Technologies and tools I use to bring ideas to life
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-16 gap-y-8">
            {skills.map((skill, index) => (
              <Skill
                key={index}
                name={skill.name}
                percentage={skill.percentage}
                color={skill.color}
                delay={index * 100}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
