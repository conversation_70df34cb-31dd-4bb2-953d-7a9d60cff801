import { useState, useEffect } from 'react';
import { supabase, isSupabaseConfigured } from '../lib/supabase';
import { Project } from '../types/database';

// Mock data for when Supabase is not configured
const mockProjects: Project[] = [
  {
    id: '1',
    title: 'E-Commerce Dashboard',
    description: 'A comprehensive dashboard for managing e-commerce operations with real-time analytics and inventory management.',
    image_url: 'src/images/dp1.png',
    category: 'dashboard',
    technologies: ['React', 'TypeScript', 'Node.js', 'PostgreSQL', 'Chart.js'],
    github_url: 'https://github.com/kibrumichael/ecommerce-dashboard',
    live_url: 'https://ecommerce-dashboard-demo.com',
    featured: true,
    created_at: '2023-08-15T00:00:00Z',
    updated_at: '2023-08-15T00:00:00Z',
    order_index: 1,
  },
  {
    id: '2',
    title: 'Modern E-Commerce Website',
    description: 'A fully responsive e-commerce platform with advanced filtering, payment integration, and user management.',
    image_url: 'src/images/project-1.jpg',
    category: 'web',
    technologies: ['Next.js', 'Tailwind CSS', 'Stripe', 'MongoDB'],
    github_url: 'https://github.com/kibrumichael/ecommerce-site',
    live_url: 'https://modern-ecommerce-demo.com',
    featured: true,
    created_at: '2023-07-20T00:00:00Z',
    updated_at: '2023-07-20T00:00:00Z',
    order_index: 2,
  },
  {
    id: '3',
    title: 'Mobile App UI Design',
    description: 'Clean and intuitive mobile app interface design with focus on user experience and accessibility.',
    image_url: 'src/images/project-2.jpg',
    category: 'design',
    technologies: ['Figma', 'Adobe XD', 'Principle', 'InVision'],
    live_url: 'https://dribbble.com/shots/mobile-app-ui',
    featured: false,
    created_at: '2023-06-10T00:00:00Z',
    updated_at: '2023-06-10T00:00:00Z',
    order_index: 3,
  },
  {
    id: '4',
    title: 'Portfolio Website Design',
    description: 'Modern portfolio website with dark mode, smooth animations, and responsive design.',
    image_url: 'src/images/project-3.jpg',
    category: 'design',
    technologies: ['React', 'Framer Motion', 'Tailwind CSS'],
    github_url: 'https://github.com/kibrumichael/portfolio-design',
    live_url: 'https://portfolio-design-demo.com',
    featured: false,
    created_at: '2023-05-25T00:00:00Z',
    updated_at: '2023-05-25T00:00:00Z',
    order_index: 4,
  },
  {
    id: '5',
    title: 'Brand Identity & Illustration',
    description: 'Complete brand identity package including logo design, color palette, and marketing materials.',
    image_url: 'src/images/project-4.jpg',
    category: 'design',
    technologies: ['Adobe Illustrator', 'Photoshop', 'InDesign'],
    live_url: 'https://behance.net/gallery/brand-identity',
    featured: false,
    created_at: '2023-04-15T00:00:00Z',
    updated_at: '2023-04-15T00:00:00Z',
    order_index: 5,
  },
  {
    id: '6',
    title: 'Task Management Web App',
    description: 'Collaborative task management application with real-time updates and team collaboration features.',
    image_url: 'src/images/project-6.jpg',
    category: 'web',
    technologies: ['Vue.js', 'Express.js', 'Socket.io', 'MySQL'],
    github_url: 'https://github.com/kibrumichael/task-manager',
    live_url: 'https://task-manager-demo.com',
    featured: true,
    created_at: '2023-03-30T00:00:00Z',
    updated_at: '2023-03-30T00:00:00Z',
    order_index: 6,
  },
];

export const useProjects = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!isSupabaseConfigured()) {
          // Use mock data when Supabase is not configured
          setProjects(mockProjects);
          setLoading(false);
          return;
        }

        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .order('order_index', { ascending: true });

        if (error) {
          throw error;
        }

        setProjects(data || []);
      } catch (err) {
        console.error('Error fetching projects:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch projects');
        // Fallback to mock data on error
        setProjects(mockProjects);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const getProjectsByCategory = (category: string) => {
    if (category === 'all') return projects;
    return projects.filter(project => project.category === category);
  };

  const getFeaturedProjects = () => {
    return projects.filter(project => project.featured);
  };

  const getProjectById = (id: string) => {
    return projects.find(project => project.id === id);
  };

  return {
    projects,
    loading,
    error,
    getProjectsByCategory,
    getFeaturedProjects,
    getProjectById,
  };
};
