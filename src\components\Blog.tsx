import React, { useState } from "react";
import { Calendar, Clock, Eye, ArrowRight, Search, Tag } from "lucide-react";
import { useBlogPosts } from "../hooks/useBlogPosts";
import { useAnalytics } from "./GoogleAnalytics";
import { format } from "date-fns";
import { useDebounce } from "use-debounce";

export const Blog: React.FC = () => {
  const { posts, loading, error, getRecentPosts, incrementViews } =
    useBlogPosts();
  const { trackBlogRead } = useAnalytics();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [debouncedSearchTerm] = useDebounce(searchTerm, 300);

  // Get all unique tags from posts
  const allTags = Array.from(new Set(posts.flatMap((post) => post.tags))).slice(
    0,
    8
  ); // Limit to 8 tags for display

  // Filter posts based on search term and selected tag
  const filteredPosts = posts.filter((post) => {
    const matchesSearch =
      !debouncedSearchTerm ||
      post.title.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(debouncedSearchTerm.toLowerCase());

    const matchesTag = !selectedTag || post.tags.includes(selectedTag);

    return matchesSearch && matchesTag;
  });

  const displayPosts = filteredPosts.slice(0, 6); // Show max 6 posts

  if (loading) {
    return (
      <section className="py-20 bg-gray-50 dark:bg-dark-800 transition-colors duration-500">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-96 mx-auto"></div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-300 dark:bg-gray-600 h-56 rounded-2xl mb-4"></div>
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section
      id="blog-section"
      className="py-20 bg-gray-50 dark:bg-dark-800 transition-colors duration-500"
    >
      <div className="container mx-auto px-4">
        <div className="text-center mb-16" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-full mb-6">
            <span className="w-2 h-2 bg-primary-500 rounded-full mr-3 animate-pulse"></span>
            <span className="text-primary-600 dark:text-primary-400 text-sm font-semibold">
              BLOG
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
            Recent <span className="text-gradient">Articles</span>
          </h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-lg">
            Insights and thoughts on web development, technology, and innovation
          </p>
        </div>

        {/* Search and Filter Section */}
        <div className="mb-12" data-aos="fade-up" data-aos-delay="100">
          <div className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <Search
                className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={20}
              />
              <input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-3 border border-gray-300 dark:border-dark-600 rounded-xl bg-white dark:bg-dark-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300"
              />
            </div>
          </div>

          {/* Tags Filter */}
          {allTags.length > 0 && (
            <div className="flex flex-wrap justify-center gap-3">
              <button
                onClick={() => setSelectedTag(null)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                  selectedTag === null
                    ? "bg-primary-500 text-white shadow-lg"
                    : "bg-white dark:bg-dark-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-dark-600 hover:border-primary-300 dark:hover:border-primary-600"
                }`}
              >
                All Topics
              </button>
              {allTags.map((tag) => (
                <button
                  key={tag}
                  onClick={() => setSelectedTag(tag)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 flex items-center ${
                    selectedTag === tag
                      ? "bg-primary-500 text-white shadow-lg"
                      : "bg-white dark:bg-dark-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-dark-600 hover:border-primary-300 dark:hover:border-primary-600"
                  }`}
                >
                  <Tag size={14} className="mr-1" />
                  {tag}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* No posts found message */}
        {displayPosts.length === 0 && !loading && (
          <div className="text-center py-16" data-aos="fade-up">
            <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 dark:bg-dark-700 rounded-full flex items-center justify-center">
              <Search size={32} className="text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No articles found
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Try adjusting your search terms or browse all topics
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {displayPosts.map((post, index) => (
            <article
              key={post.id}
              className="group bg-white dark:bg-dark-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl border border-gray-200 dark:border-dark-700 transition-all duration-500 hover:-translate-y-3"
              data-aos="fade-up"
              data-aos-delay={index * 100}
            >
              <a
                href={`/blog/${post.slug}`}
                onClick={() => {
                  trackBlogRead(post.title, post.read_time);
                  incrementViews(post.slug);
                }}
                className="block relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>
                <div className="absolute top-4 right-4 bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-semibold z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  Read Article
                </div>
                <img
                  src={post.image_url}
                  alt={post.title}
                  className="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
                />
              </a>

              <div className="p-6">
                <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <span className="flex items-center bg-gray-100 dark:bg-dark-800 px-3 py-1 rounded-full">
                    <Calendar size={12} className="mr-1" />
                    {format(new Date(post.created_at), "MMM dd, yyyy")}
                  </span>
                  <span className="flex items-center bg-gray-100 dark:bg-dark-800 px-3 py-1 rounded-full">
                    <Clock size={12} className="mr-1" />
                    {post.read_time} min read
                  </span>
                  <span className="flex items-center bg-gray-100 dark:bg-dark-800 px-3 py-1 rounded-full">
                    <Eye size={12} className="mr-1" />
                    {post.views}
                  </span>
                </div>

                {/* Tags */}
                {post.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 text-xs rounded-full border border-primary-200 dark:border-primary-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}

                <h3 className="text-xl font-bold mb-3 text-gray-900 dark:text-white group-hover:text-primary-500 transition-colors duration-300 leading-tight">
                  <a
                    href={`/blog/${post.slug}`}
                    onClick={() => {
                      trackBlogRead(post.title, post.read_time);
                      incrementViews(post.slug);
                    }}
                  >
                    {post.title}
                  </a>
                </h3>

                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed line-clamp-3">
                  {post.excerpt}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                      K
                    </div>
                    <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                      Kibru Michael
                    </span>
                  </div>
                  <a
                    href={`/blog/${post.slug}`}
                    onClick={() => {
                      trackBlogRead(post.title, post.read_time);
                      incrementViews(post.slug);
                    }}
                    className="inline-flex items-center text-primary-500 hover:text-primary-600 dark:text-primary-400 dark:hover:text-primary-300 font-medium transition-all duration-300 group/link"
                  >
                    Read More
                    <ArrowRight
                      size={16}
                      className="ml-1 group-hover/link:translate-x-1 transition-transform duration-300"
                    />
                  </a>
                </div>
              </div>
            </article>
          ))}
        </div>

        <div className="text-center mt-16" data-aos="fade-up">
          <a
            href="#blog-section"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-full font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-glow"
          >
            View All Posts
            <ArrowRight size={18} className="ml-2" />
          </a>
        </div>
      </div>
    </section>
  );
};
