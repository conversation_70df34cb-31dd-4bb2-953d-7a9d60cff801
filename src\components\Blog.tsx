import React from "react";
import { Calendar, User, MessageCircle, ArrowRight } from "lucide-react";

type BlogPost = {
  id: number;
  title: string;
  excerpt: string;
  image: string;
  date: string;
  author: string;
  comments: number;
};

export const Blog: React.FC = () => {
  const blogPosts: BlogPost[] = [
    {
      id: 1,
      title: "The Rise of Serverless Architecture",
      excerpt:
        "Serverless architecture has been gaining significant traction in the web development world, and for good reason.",
      image: "src/images/image1.jpg",
      date: "June 21, 2023",
      author: "Admin",
      comments: 3,
    },
    {
      id: 2,
      title: "Demystifying Machine Learning for Web Developers",
      excerpt:
        'The term "Machine Learning" can sound intimidating, but at its core, it\'s about enabling computers to learn from data without explicit programming.',
      image: "src/images/image2.jpg",
      date: "July 15, 2023",
      author: "Admin",
      comments: 5,
    },
    {
      id: 3,
      title: "The Synergy Between AI and Full-Stack Development",
      excerpt:
        "The intersection of Artificial Intelligence (AI) and full-stack development is creating exciting new possibilities.",
      image: "src/images/image3.jpg",
      date: "August 3, 2023",
      author: "Admin",
      comments: 7,
    },
  ];

  return (
    <section
      id="blog-section"
      className="py-20 bg-gray-50 dark:bg-dark-800 transition-colors duration-500"
    >
      <div className="container mx-auto px-4">
        <div className="text-center mb-16" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-full mb-6">
            <span className="w-2 h-2 bg-primary-500 rounded-full mr-3 animate-pulse"></span>
            <span className="text-primary-600 dark:text-primary-400 text-sm font-semibold">
              BLOG
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
            Recent <span className="text-gradient">Articles</span>
          </h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-lg">
            Insights and thoughts on web development, technology, and innovation
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post, index) => (
            <article
              key={post.id}
              className="group bg-white dark:bg-dark-900 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl border border-gray-200 dark:border-dark-700 transition-all duration-500 hover:-translate-y-3"
              data-aos="fade-up"
              data-aos-delay={index * 100}
            >
              <a
                href={`single.html?post=${post.id}`}
                className="block relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>
                <div className="absolute top-4 right-4 bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-semibold z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  Read Article
                </div>
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-56 object-cover transition-transform duration-700 group-hover:scale-110"
                />
              </a>

              <div className="p-6">
                <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <span className="flex items-center bg-gray-100 dark:bg-dark-800 px-3 py-1 rounded-full">
                    <Calendar size={12} className="mr-1" />
                    {post.date}
                  </span>
                  <span className="flex items-center bg-gray-100 dark:bg-dark-800 px-3 py-1 rounded-full">
                    <MessageCircle size={12} className="mr-1" />
                    {post.comments}
                  </span>
                </div>

                <h3 className="text-xl font-bold mb-3 text-gray-900 dark:text-white group-hover:text-primary-500 transition-colors duration-300 leading-tight">
                  <a href={`single.html?post=${post.id}`}>{post.title}</a>
                </h3>

                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed line-clamp-3">
                  {post.excerpt}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                      {post.author.charAt(0)}
                    </div>
                    <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                      {post.author}
                    </span>
                  </div>
                  <a
                    href={`single.html?post=${post.id}`}
                    className="inline-flex items-center text-primary-500 hover:text-primary-600 dark:text-primary-400 dark:hover:text-primary-300 font-medium transition-all duration-300 group/link"
                  >
                    Read More
                    <ArrowRight
                      size={16}
                      className="ml-1 group-hover/link:translate-x-1 transition-transform duration-300"
                    />
                  </a>
                </div>
              </div>
            </article>
          ))}
        </div>

        <div className="text-center mt-16" data-aos="fade-up">
          <a
            href="#blog-section"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-full font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-glow"
          >
            View All Posts
            <ArrowRight size={18} className="ml-2" />
          </a>
        </div>
      </div>
    </section>
  );
};
