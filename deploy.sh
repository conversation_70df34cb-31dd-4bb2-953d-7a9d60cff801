#!/bin/bash

# 🚀 Portfolio Deployment Script
# This script prepares your portfolio for deployment

echo "🚀 Preparing <PERSON><PERSON><PERSON> for Deployment..."
echo "=================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js version: $(node --version)"
echo "✅ npm version: $(npm --version)"
echo ""

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"
echo ""

# Run build
echo "🔨 Building the project..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build completed successfully"
echo ""

# Check if dist folder exists
if [ ! -d "dist" ]; then
    echo "❌ dist folder not found"
    exit 1
fi

echo "📊 Build Statistics:"
echo "==================="
du -sh dist/
echo ""

echo "📁 Files in dist folder:"
ls -la dist/
echo ""

echo "🎉 Your portfolio is ready for deployment!"
echo "=========================================="
echo ""
echo "📋 Next Steps:"
echo "1. Go to https://netlify.com"
echo "2. Sign up/login with GitHub"
echo "3. Drag the 'dist' folder to Netlify"
echo "4. Configure environment variables"
echo "5. Your site will be live!"
echo ""
echo "🌐 Alternative: Use Git integration"
echo "1. Push your code to GitHub"
echo "2. Connect repository to Netlify"
echo "3. Automatic deployments on every push"
echo ""
echo "📚 For detailed instructions, see: NETLIFY_DEPLOYMENT.md"
